"""
CRUD operations for database models.

This module provides Create, Read, Update, Delete operations
for all database models with proper error handling and validation.
"""

from typing import List, Optional, Dict, Any, TypeVar, Generic, Type
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime
import uuid
from abc import ABC, abstractmethod

from models import User, Chat, Message, DatabaseConnection, QueryHistory, APIKey, Tenant, TenantUser, AdminUser
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Generic type for database models
ModelType = TypeVar("ModelType")

class BaseCRUD(Generic[ModelType], ABC):
    """
    Abstract base class for CRUD operations.
    Eliminates logical duplication across all CRUD classes.
    """

    def __init__(self, model: Type[ModelType]):
        self.model = model

    def create_entity(self, db: Session, **kwargs) -> ModelType:
        """Generic entity creation with transaction handling."""
        # Handle password hashing if password field exists
        if 'password' in kwargs and hasattr(self.model, 'password'):
            kwargs['password'] = pwd_context.hash(kwargs['password'])
        elif 'password' in kwargs and hasattr(self.model, 'hashed_password'):
            kwargs['hashed_password'] = pwd_context.hash(kwargs.pop('password'))

        entity = self.model(**kwargs)
        db.add(entity)
        db.commit()
        db.refresh(entity)
        return entity

    def get_by_id(self, db: Session, entity_id: Any) -> Optional[ModelType]:
        """Generic get by ID."""
        return db.query(self.model).filter(self.model.id == entity_id).first()

    def get_by_email(self, db: Session, email: str) -> Optional[ModelType]:
        """Generic get by email (if model has email field)."""
        if hasattr(self.model, 'email'):
            return db.query(self.model).filter(self.model.email == email).first()
        return None

    def get_all(self, db: Session, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Generic get all with pagination."""
        return db.query(self.model).offset(skip).limit(limit).all()

    def update_entity(self, db: Session, entity_id: Any, **kwargs) -> Optional[ModelType]:
        """Generic entity update."""
        entity = self.get_by_id(db, entity_id)
        if not entity:
            return None

        # Handle password hashing if password field exists
        if 'password' in kwargs and hasattr(self.model, 'password'):
            kwargs['password'] = pwd_context.hash(kwargs['password'])
        elif 'password' in kwargs and hasattr(self.model, 'hashed_password'):
            kwargs['hashed_password'] = pwd_context.hash(kwargs.pop('password'))

        for key, value in kwargs.items():
            if hasattr(entity, key):
                setattr(entity, key, value)

        db.commit()
        db.refresh(entity)
        return entity

    def delete_entity(self, db: Session, entity_id: Any) -> bool:
        """Generic entity deletion."""
        entity = self.get_by_id(db, entity_id)
        if not entity:
            return False

        db.delete(entity)
        db.commit()
        return True


class UserCRUD(BaseCRUD[User]):
    """CRUD operations for User model."""

    def __init__(self):
        super().__init__(User)

    def create_user(self, db: Session, email: str, password: str, full_name: str = None, username: str = None) -> User:
        """Create a new user."""
        return self.create_entity(
            db=db,
            email=email,
            username=username,
            full_name=full_name,
            password=password,  # Will be automatically hashed by base class
            is_active=True,
            is_superuser=False
        )

    def get_user_by_email(self, db: Session, email: str) -> Optional[User]:
        """Get user by email."""
        return self.get_by_email(db, email)

    def get_user_by_id(self, db: Session, user_id: str) -> Optional[User]:
        """Get user by ID."""
        return self.get_by_id(db, user_id)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify password."""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def update_last_login(db: Session, user_id: str) -> bool:
        """Update user's last login timestamp."""
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            user.last_login = datetime.now()
            db.commit()
            return True
        return False


class ChatCRUD:
    """CRUD operations for Chat model."""
    
    @staticmethod
    def create_chat(db: Session, user_id: int, title: str = "New Chat", database_connection_id: str = None) -> Chat:
        """Create a new chat."""
        chat = Chat(
            tenant_user_id=user_id,
            title=title,
            database_connection_id=database_connection_id
        )
        db.add(chat)
        db.commit()
        db.refresh(chat)
        return chat
    
    @staticmethod
    def get_user_chats(db: Session, user_id: int, include_archived: bool = False) -> List[Chat]:
        """Get all chats for a user."""
        query = db.query(Chat).filter(Chat.tenant_user_id == user_id)
        # Note: is_archived field doesn't exist in current model, so we'll get all chats
        return query.order_by(desc(Chat.updated_at)).all()
    
    @staticmethod
    def get_chat_by_id(db: Session, chat_id: int, user_id: int = None) -> Optional[Chat]:
        """Get chat by ID, optionally filtered by user."""
        query = db.query(Chat).filter(Chat.id == chat_id)
        if user_id:
            query = query.filter(Chat.tenant_user_id == user_id)
        return query.first()
    
    @staticmethod
    def update_chat(db: Session, chat_id: int, user_id: int, **updates) -> Optional[Chat]:
        """Update chat properties."""
        chat = db.query(Chat).filter(and_(Chat.id == chat_id, Chat.tenant_user_id == user_id)).first()
        if chat:
            for key, value in updates.items():
                if hasattr(chat, key):
                    setattr(chat, key, value)
            chat.updated_at = datetime.now()
            db.commit()
            db.refresh(chat)
        return chat
    
    @staticmethod
    def delete_chat(db: Session, chat_id: int, user_id: int) -> bool:
        """Delete a chat and all its messages."""
        chat = db.query(Chat).filter(and_(Chat.id == chat_id, Chat.tenant_user_id == user_id)).first()
        if chat:
            # Delete associated messages first
            db.query(Message).filter(Message.chat_id == chat_id).delete()
            db.delete(chat)
            db.commit()
            return True
        return False

    @staticmethod
    def get_all_chats(db: Session, skip: int = 0, limit: int = 1000) -> List[Chat]:
        """Get all chats for admin purposes."""
        return db.query(Chat).order_by(desc(Chat.updated_at)).offset(skip).limit(limit).all()


class MessageCRUD:
    """CRUD operations for Message model."""
    
    @staticmethod
    def create_message(db: Session, chat_id: int, role: str, content: str,
                      sql_query: str = None, query_result: Dict = None, tenant_user_id: int = None) -> Message:
        """Create a new message."""
        # Store SQL query in metadata if provided
        metadata_info = {}
        if sql_query:
            metadata_info['sql_query'] = sql_query
        if query_result:
            metadata_info['query_result'] = query_result

        message = Message(
            chat_id=chat_id,
            tenant_user_id=tenant_user_id,
            role=role,
            content=content,
            metadata_info=metadata_info if metadata_info else None
        )
        db.add(message)
        db.commit()
        db.refresh(message)

        # Update chat's updated_at timestamp
        chat = db.query(Chat).filter(Chat.id == chat_id).first()
        if chat:
            chat.updated_at = datetime.now()
            db.commit()

        return message
    
    @staticmethod
    def get_chat_messages(db: Session, chat_id: int, limit: int = 100) -> List[Message]:
        """Get messages for a chat."""
        return db.query(Message).filter(Message.chat_id == chat_id)\
                 .order_by(Message.created_at)\
                 .limit(limit).all()
    
    @staticmethod
    def get_message_by_id(db: Session, message_id: int) -> Optional[Message]:
        """Get message by ID."""
        return db.query(Message).filter(Message.id == message_id).first()

    @staticmethod
    def get_all_messages(db: Session, skip: int = 0, limit: int = 1000) -> List[Message]:
        """Get all messages for admin purposes."""
        return db.query(Message).order_by(desc(Message.created_at)).offset(skip).limit(limit).all()


class DatabaseConnectionCRUD:
    """CRUD operations for DatabaseConnection model."""
    
    @staticmethod
    def create_connection(db: Session, user_id: str, name: str, db_type: str, 
                         database_name: str, **kwargs) -> DatabaseConnection:
        """Create a new database connection."""
        connection = DatabaseConnection(
            user_id=user_id,
            name=name,
            db_type=db_type,
            database_name=database_name,
            **kwargs
        )
        db.add(connection)
        db.commit()
        db.refresh(connection)
        return connection
    
    @staticmethod
    def get_user_connections(db: Session, user_id: str, active_only: bool = True) -> List[DatabaseConnection]:
        """Get all database connections for a user."""
        query = db.query(DatabaseConnection).filter(DatabaseConnection.user_id == user_id)
        if active_only:
            query = query.filter(DatabaseConnection.is_active == True)
        return query.order_by(desc(DatabaseConnection.created_at)).all()
    
    @staticmethod
    def get_connection_by_id(db: Session, connection_id: str, user_id: str = None) -> Optional[DatabaseConnection]:
        """Get database connection by ID."""
        query = db.query(DatabaseConnection).filter(DatabaseConnection.id == connection_id)
        if user_id:
            query = query.filter(DatabaseConnection.user_id == user_id)
        return query.first()


class QueryHistoryCRUD:
    """CRUD operations for QueryHistory model."""
    
    @staticmethod
    def create_query_record(db: Session, user_id: str, natural_language_query: str,
                           generated_sql: str = None, execution_status: str = "pending",
                           **kwargs) -> QueryHistory:
        """Create a new query history record."""
        query_record = QueryHistory(
            user_id=user_id,
            natural_language_query=natural_language_query,
            generated_sql=generated_sql,
            execution_status=execution_status,
            **kwargs
        )
        db.add(query_record)
        db.commit()
        db.refresh(query_record)
        return query_record
    
    @staticmethod
    def get_user_query_history(db: Session, user_id: str, limit: int = 50) -> List[QueryHistory]:
        """Get query history for a user."""
        return db.query(QueryHistory).filter(QueryHistory.user_id == user_id)\
                 .order_by(desc(QueryHistory.created_at))\
                 .limit(limit).all()


class APIKeyCRUD:
    """CRUD operations for APIKey model."""
    
    @staticmethod
    def create_api_key(db: Session, user_id: str, service_name: str, 
                      key_name: str, encrypted_key: str) -> APIKey:
        """Create a new API key record."""
        api_key = APIKey(
            user_id=user_id,
            service_name=service_name,
            key_name=key_name,
            encrypted_key=encrypted_key,
            is_active=True,
            usage_count=0
        )
        db.add(api_key)
        db.commit()
        db.refresh(api_key)
        return api_key
    
    @staticmethod
    def get_user_api_keys(db: Session, user_id: str, service_name: str = None) -> List[APIKey]:
        """Get API keys for a user."""
        query = db.query(APIKey).filter(APIKey.user_id == user_id)
        if service_name:
            query = query.filter(APIKey.service_name == service_name)
        return query.filter(APIKey.is_active == True).all()


# Convenience functions
def get_user_crud() -> UserCRUD:
    """Get UserCRUD instance."""
    return UserCRUD()

def get_chat_crud() -> ChatCRUD:
    """Get ChatCRUD instance."""
    return ChatCRUD()

def get_message_crud() -> MessageCRUD:
    """Get MessageCRUD instance."""
    return MessageCRUD()

def get_db_connection_crud() -> DatabaseConnectionCRUD:
    """Get DatabaseConnectionCRUD instance."""
    return DatabaseConnectionCRUD()

def get_query_history_crud() -> QueryHistoryCRUD:
    """Get QueryHistoryCRUD instance."""
    return QueryHistoryCRUD()

def get_api_key_crud() -> APIKeyCRUD:
    """Get APIKeyCRUD instance."""
    return APIKeyCRUD()


class TenantCRUD(BaseCRUD[Tenant]):
    """CRUD operations for Tenant model."""

    def __init__(self):
        super().__init__(Tenant)

    def create_tenant(self, db: Session, employer_name: str, email: str, size: str = None, phone: str = None, status: str = 'active') -> Tenant:
        """Create a new tenant."""
        return self.create_entity(
            db=db,
            employer_name=employer_name,
            email=email,
            size=size,
            phone=phone,
            status=status
        )

    def get_tenant_by_id(self, db: Session, tenant_id: int) -> Optional[Tenant]:
        """Get tenant by ID."""
        return self.get_by_id(db, tenant_id)

    def get_tenant_by_email(self, db: Session, email: str) -> Optional[Tenant]:
        """Get tenant by email."""
        return self.get_by_email(db, email)

    def get_tenants(self, db: Session, skip: int = 0, limit: int = 100) -> List[Tenant]:
        """Get all tenants with pagination."""
        return self.get_all(db, skip, limit)

    @staticmethod
    def update_tenant(db: Session, tenant_id: int, **kwargs) -> Optional[Tenant]:
        """Update tenant information."""
        tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            return None

        for key, value in kwargs.items():
            if hasattr(tenant, key) and value is not None:
                setattr(tenant, key, value)

        db.commit()
        db.refresh(tenant)
        return tenant

    @staticmethod
    def delete_tenant(db: Session, tenant_id: int) -> bool:
        """Delete a tenant."""
        tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            return False

        db.delete(tenant)
        db.commit()
        return True


class TenantUserCRUD(BaseCRUD[TenantUser]):
    """CRUD operations for TenantUser model."""

    def __init__(self):
        super().__init__(TenantUser)

    def create_tenant_user(self, db: Session, name: str, role: str, email: str, tenant_id: int, phone: str = None, status: str = 'active', password: str = 'defaultpassword123') -> TenantUser:
        """Create a new tenant user."""
        return self.create_entity(
            db=db,
            name=name,
            role=role,
            email=email,
            phone=phone,
            status=status,
            tenant_id=tenant_id,
            password=password,  # Will be automatically hashed by base class
            is_superuser=False
        )

    def get_tenant_user_by_id(self, db: Session, user_id: int) -> Optional[TenantUser]:
        """Get tenant user by ID."""
        return self.get_by_id(db, user_id)

    def get_tenant_user_by_email(self, db: Session, email: str) -> Optional[TenantUser]:
        """Get tenant user by email."""
        return self.get_by_email(db, email)

    def get_tenant_users_by_tenant(self, db: Session, tenant_id: int) -> List[TenantUser]:
        """Get all users for a specific tenant."""
        return db.query(TenantUser).filter(TenantUser.tenant_id == tenant_id).all()

    def get_tenant_users(self, db: Session, skip: int = 0, limit: int = 100) -> List[TenantUser]:
        """Get all tenant users with pagination."""
        return self.get_all(db, skip, limit)

    @staticmethod
    def update_tenant_user(db: Session, user_id: int, **kwargs) -> Optional[TenantUser]:
        """Update tenant user information."""
        tenant_user = db.query(TenantUser).filter(TenantUser.id == user_id).first()
        if not tenant_user:
            return None

        for key, value in kwargs.items():
            if hasattr(tenant_user, key) and value is not None:
                setattr(tenant_user, key, value)

        db.commit()
        db.refresh(tenant_user)
        return tenant_user

    @staticmethod
    def delete_tenant_user(db: Session, user_id: int) -> bool:
        """Delete a tenant user."""
        tenant_user = db.query(TenantUser).filter(TenantUser.id == user_id).first()
        if not tenant_user:
            return False

        db.delete(tenant_user)
        db.commit()
        return True

    @staticmethod
    def get_users_by_role(db: Session, tenant_id: str, role: str) -> List[TenantUser]:
        """Get users by role within a tenant."""
        return db.query(TenantUser).filter(
            and_(TenantUser.tenant_id == tenant_id, TenantUser.role == role)
        ).all()


def get_tenant_crud() -> TenantCRUD:
    """Get TenantCRUD instance."""
    return TenantCRUD()

def get_tenant_user_crud() -> TenantUserCRUD:
    """Get TenantUserCRUD instance."""
    return TenantUserCRUD()


class AdminUserCRUD(BaseCRUD[AdminUser]):
    """CRUD operations for AdminUser model."""

    def __init__(self):
        super().__init__(AdminUser)

    def create_admin_user(self, db: Session, name: str, email: str, password: str, role: str, phone: str = None, status: str = 'active') -> AdminUser:
        """Create a new admin user."""
        return self.create_entity(
            db=db,
            name=name,
            email=email,
            password=password,  # Will be automatically hashed by base class
            phone=phone,
            role=role,
            status=status
        )

    def get_admin_user_by_id(self, db: Session, user_id: int) -> Optional[AdminUser]:
        """Get admin user by ID."""
        return self.get_by_id(db, user_id)

    def get_admin_user_by_email(self, db: Session, email: str) -> Optional[AdminUser]:
        """Get admin user by email."""
        return self.get_by_email(db, email)

    def get_admin_users(self, db: Session, skip: int = 0, limit: int = 100) -> List[AdminUser]:
        """Get all admin users with pagination."""
        return self.get_all(db, skip, limit)

    def get_admin_users_by_status(self, db: Session, status: str) -> List[AdminUser]:
        """Get admin users by status."""
        return db.query(AdminUser).filter(AdminUser.status == status).all()

    def get_admin_users_by_role(self, db: Session, role: str) -> List[AdminUser]:
        """Get admin users by role."""
        return db.query(AdminUser).filter(AdminUser.role == role).all()

    def update_admin_user(self, db: Session, user_id: int, **kwargs) -> Optional[AdminUser]:
        """Update admin user information."""
        return self.update_entity(db, user_id, **kwargs)

    def delete_admin_user(self, db: Session, user_id: int) -> bool:
        """Delete an admin user."""
        return self.delete_entity(db, user_id)

    def activate_admin_user(self, db: Session, user_id: int) -> Optional[AdminUser]:
        """Activate an admin user."""
        return self.update_admin_user(db, user_id, status='active')

    def deactivate_admin_user(self, db: Session, user_id: int) -> Optional[AdminUser]:
        """Deactivate an admin user."""
        return self.update_admin_user(db, user_id, status='inactive')

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)

    def update_password(self, db: Session, user_id: int, new_password: str) -> Optional[AdminUser]:
        """Update admin user password."""
        return self.update_entity(db, user_id, password=new_password)  # Will be hashed automatically

    def authenticate_admin_user(self, db: Session, email: str, password: str) -> Optional[AdminUser]:
        """Authenticate admin user by email and password."""
        admin_user = self.get_admin_user_by_email(db, email)
        if admin_user and self.verify_password(password, admin_user.password):
            return admin_user
        return None


def get_admin_user_crud() -> AdminUserCRUD:
    """Get AdminUserCRUD instance."""
    return AdminUserCRUD()
