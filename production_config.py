"""
Production Configuration Module

This module provides production-specific configuration and utilities
for running the FastAPI application in a production environment.
"""

import os
import logging
import signal
import sys
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI
import multiprocessing

# Configure production logging
def setup_production_logging():
    """Configure logging for production environment."""
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    log_format = os.getenv("LOG_FORMAT", "json").lower()
    
    if log_format == "json":
        # JSON logging for production (better for log aggregation)
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='{"timestamp": "%(asctime)s", "level": "%(levelname)s", "module": "%(name)s", "message": "%(message)s"}',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    else:
        # Standard logging format
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # Set specific loggers
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("uvicorn.error").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)

class ProductionConfig:
    """Production configuration settings."""
    
    def __init__(self):
        self.app_env = os.getenv("APP_ENV", "production")
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))
        self.workers = int(os.getenv("WORKERS", str(multiprocessing.cpu_count())))
        self.reload = os.getenv("RELOAD", "false").lower() == "true"
        
        # Server settings
        self.max_requests = int(os.getenv("MAX_REQUESTS", "1000"))
        self.max_requests_jitter = int(os.getenv("MAX_REQUESTS_JITTER", "100"))
        self.timeout = int(os.getenv("TIMEOUT", "30"))
        self.keep_alive = int(os.getenv("KEEP_ALIVE", "5"))
        
        # SSL/TLS settings
        self.ssl_keyfile = os.getenv("SSL_KEYFILE")
        self.ssl_certfile = os.getenv("SSL_CERTFILE")
        self.ssl_ca_certs = os.getenv("SSL_CA_CERTS")
        
        # Security settings
        self.trusted_hosts = self._parse_trusted_hosts()
        
    def _parse_trusted_hosts(self) -> list:
        """Parse trusted hosts from environment variable."""
        hosts_str = os.getenv("TRUSTED_HOSTS", "")
        if not hosts_str:
            return ["*"]  # Allow all hosts if not specified
        return [host.strip() for host in hosts_str.split(",") if host.strip()]
    
    def get_uvicorn_config(self) -> Dict[str, Any]:
        """Get Uvicorn configuration for production."""
        config = {
            "host": self.host,
            "port": self.port,
            "log_level": os.getenv("LOG_LEVEL", "info").lower(),
            "access_log": True,
            "use_colors": False,  # Disable colors in production logs
            "server_header": False,  # Don't expose server info
            "date_header": True,
        }
        
        # Add SSL configuration if provided
        if self.ssl_keyfile and self.ssl_certfile:
            config.update({
                "ssl_keyfile": self.ssl_keyfile,
                "ssl_certfile": self.ssl_certfile,
                "ssl_ca_certs": self.ssl_ca_certs,
            })
        
        return config
    
    def get_gunicorn_config(self) -> Dict[str, Any]:
        """Get Gunicorn configuration for production."""
        return {
            "bind": f"{self.host}:{self.port}",
            "workers": self.workers,
            "worker_class": "uvicorn.workers.UvicornWorker",
            "max_requests": self.max_requests,
            "max_requests_jitter": self.max_requests_jitter,
            "timeout": self.timeout,
            "keepalive": self.keep_alive,
            "preload_app": True,
            "access_logfile": "-",
            "error_logfile": "-",
            "log_level": os.getenv("LOG_LEVEL", "info").lower(),
        }

class GracefulShutdownHandler:
    """Handle graceful shutdown of the application."""
    
    def __init__(self):
        self.shutdown = False
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logging.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown = True
    
    def should_shutdown(self) -> bool:
        """Check if application should shutdown."""
        return self.shutdown

class HealthChecker:
    """Production health checking utilities."""
    
    @staticmethod
    def check_database_health() -> Dict[str, Any]:
        """Check database connectivity."""
        try:
            from mysql_manager import get_mysql_manager
            manager = get_mysql_manager()
            result = manager.test_connection()
            return {
                "database": {
                    "status": result["status"],
                    "details": result.get("mysql_version", "Unknown")
                }
            }
        except Exception as e:
            return {
                "database": {
                    "status": "error",
                    "details": str(e)
                }
            }
    
    @staticmethod
    def check_dependencies() -> Dict[str, Any]:
        """Check external dependencies."""
        checks = {}
        
        # Check OpenAI API key
        openai_key = os.getenv("OPENAI_API_KEY")
        checks["openai"] = {
            "status": "ok" if openai_key and openai_key != "your_openai_api_key_here" else "error",
            "details": "API key configured" if openai_key else "API key missing"
        }
        
        # Check Redis (if configured)
        redis_url = os.getenv("REDIS_URL")
        if redis_url:
            try:
                import redis
                r = redis.from_url(redis_url)
                r.ping()
                checks["redis"] = {"status": "ok", "details": "Connected"}
            except ImportError:
                checks["redis"] = {"status": "error", "details": "Redis module not installed"}
            except Exception as e:
                checks["redis"] = {"status": "error", "details": str(e)}
        else:
            # Redis is not configured, but that's okay for development
            try:
                import redis
                checks["redis"] = {"status": "ok", "details": "Module available but not configured"}
            except ImportError:
                checks["redis"] = {"status": "error", "details": "No module named 'redis'"}
        
        return checks
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """Get system information for monitoring."""
        import psutil
        import platform
        
        return {
            "system": {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "cpu_count": multiprocessing.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "memory_available": psutil.virtual_memory().available,
                "disk_usage": psutil.disk_usage('/').percent
            }
        }

def create_production_app() -> FastAPI:
    """Create FastAPI application with production configuration."""
    from main import app
    
    # Set up production logging
    setup_production_logging()
    
    # Log startup information
    config = ProductionConfig()
    logging.info(f"Starting application in {config.app_env} mode")
    logging.info(f"Workers: {config.workers}, Host: {config.host}, Port: {config.port}")
    
    return app

def run_production_server():
    """Run the application with production server configuration."""
    config = ProductionConfig()
    
    # Set up logging
    setup_production_logging()
    
    # Create application
    app = create_production_app()
    
    # Set up graceful shutdown
    shutdown_handler = GracefulShutdownHandler()
    
    # Get server configuration
    server_config = config.get_uvicorn_config()
    
    logging.info("Starting production server...")
    logging.info(f"Configuration: {server_config}")
    
    try:
        # Run with Uvicorn
        uvicorn.run(
            "main:app",
            **server_config,
            reload=config.reload
        )
    except KeyboardInterrupt:
        logging.info("Server stopped by user")
    except Exception as e:
        logging.error(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_production_server()
