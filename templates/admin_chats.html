{% extends "base.html" %}

{% block title %}Chats - Admin{% endblock %}
{% block page_title %}Chats Management{% endblock %}

{% block content %}
<div class="row">
    <!-- Main Content -->
    <div class="col-lg-9">
        <!-- Chats Table -->
        <div class="card shadow-sm">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    Chats ({{ chats|length }} found)
                </h6>
            </div>
            <div class="card-body p-0">
                {% if chats %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>User</th>
                                    <th>Messages</th>
                                    <th>Created</th>
                                    <th>Updated</th>
                                    <th class="table-actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for chat in chats %}
                                <tr>
                                    <td>
                                        <code class="text-muted small">{{ chat.id }}</code>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <div class="fw-bold">{{ chat.title }}</div>
                                                {% if chat.description %}
                                                    <small class="text-muted">{{ chat.description[:50] }}{% if chat.description|length > 50 %}...{% endif %}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ chat.user_name }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ chat.message_count }} messages</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ chat.created_at.strftime('%Y-%m-%d %H:%M') if chat.created_at else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ chat.updated_at.strftime('%Y-%m-%d %H:%M') if chat.updated_at else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/admin/chats/{{ chat.id }}" class="btn btn-outline-primary btn-sm" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No chats found</h5>
                        <p class="text-muted">No chats match your current filters.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Total Count -->
        {% if chats %}
        <div class="mt-3 text-center">
            <small class="text-muted">Total: {{ chats|length }} chats</small>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar Filters -->
    <div class="col-lg-3">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-funnel me-2"></i>Filters
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="/admin/chats">
                    <!-- Search -->
                    <div class="mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.query_params.get('search', '') }}" 
                               placeholder="Search by title or description...">
                    </div>

                    <!-- User Filter -->
                    <div class="mb-3">
                        <label for="user_id" class="form-label">User</label>
                        <select class="form-select" id="user_id" name="user_id">
                            <option value="">All Users</option>
                            {% for user in all_users %}
                                <option value="{{ user.id }}" 
                                        {% if request.query_params.get('user_id') == user.id|string %}selected{% endif %}>
                                    {{ user.name }} ({{ user.email }})
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Filter Buttons -->
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>Apply Filters
                        </button>
                        <a href="/admin/chats" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card shadow-sm mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bar-chart me-2"></i>Quick Stats
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-2">
                        <div class="border rounded p-2">
                            <div class="h5 mb-0 text-primary">{{ chats|length }}</div>
                            <small class="text-muted">Total Chats</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.table-actions {
    width: 120px;
}
</style>
{% endblock %}
