<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Dashboard{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            border-radius: 0.375rem;
            margin: 0.125rem 0;
        }
        
        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #212529;
        }
        
        .sidebar .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
        
        .main-content {
            min-height: 100vh;
        }
        
        .card-stat {
            border-left: 4px solid #0d6efd;
        }
        
        .card-stat.success {
            border-left-color: #198754;
        }
        
        .card-stat.warning {
            border-left-color: #ffc107;
        }
        
        .card-stat.danger {
            border-left-color: #dc3545;
        }
        
        .table-actions {
            white-space: nowrap;
        }
        
        .badge-role {
            font-size: 0.75em;
        }
        
        .navbar-brand {
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand px-3 mb-3">
                        <i class="bi bi-building"></i>
                        Admin Dashboard
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/admin' %}active{% endif %}" href="/admin">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/tenants' in request.url.path %}active{% endif %}" href="/admin/tenants">
                                <i class="bi bi-building me-2"></i>
                                Tenants
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/users' in request.url.path %}active{% endif %}" href="/admin/users">
                                <i class="bi bi-people me-2"></i>
                                Tenant Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/admin-users' in request.url.path %}active{% endif %}" href="/admin/admin-users">
                                <i class="bi bi-shield-check me-2"></i>
                                Admin Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/chats' in request.url.path %}active{% endif %}" href="/admin/chats">
                                <i class="bi bi-chat-dots me-2"></i>
                                Chats
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/messages' in request.url.path %}active{% endif %}" href="/admin/messages">
                                <i class="bi bi-chat-text me-2"></i>
                                Messages
                            </a>
                        </li>
                    </ul>

                    <hr>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/docs" target="_blank">
                                <i class="bi bi-book me-2"></i>
                                API Docs
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Top navbar -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}Dashboard{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>

                <!-- Flash messages -->
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Confirm delete actions
        function confirmDelete(itemName) {
            return confirm(`Are you sure you want to delete "${itemName}"? This action cannot be undone.`);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
