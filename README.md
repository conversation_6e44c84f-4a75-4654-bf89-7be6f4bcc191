My SaaS Backend API
[![Tests](https://img.shields.io/badge/tests-passing-green)](tests/) [![Deploy](https://img.shields.io/badge/deploy-ready-blue)](docker-compose.prod.yml)

## Introduction
This is the main API repository for My SaaS Backend. It is built using Python FastAPI, MySQL and OpenAI. This application is fully dockerized. It uses docker-compose to manage the containers.

## Setting up the project

### Step 1
Install your preferred IDE. We recommend using PyCharm or VS Code.

### Step 2
Install Docker Desktop

### Step 3
Clone the repository via terminal & cd into the directory

```bash
git clone <your-repository-url>
cd My_SaaS_Backend
```

### Step 4
Set up environment variables

```bash
cp .env.production .env
# Edit .env with your configuration (never commit this file)
```

### Step 5
Build & start the containers

```bash
# Development
docker-compose -f docker-compose.dev.yml up --build -d

# Production
docker-compose -f docker-compose.prod.yml up --build -d
```

### Step 6
Setup testing

```bash
docker exec -it my_saas_backend-app-1 /bin/bash
> python setup_mysql_all_envs.py --env test
> python run_tests.py --type all
```

## Useful commands:
- **Building docker containers** - `docker-compose -f docker-compose.dev.yml up --build -d`
- **View all containers** - `docker ps -a`
- **Stop all containers** - `docker stop $(docker ps -aq)`
- **Prune all stopped containers** - `docker system prune -a`
- **Login onto API container** - `docker exec -it my_saas_backend-app-1 /bin/bash`
- **Running tests locally** - `python3 run_tests.py --type unit --verbose`
- **Database migrations** - `alembic upgrade head`
- **Generate migration** - `alembic revision --autogenerate -m "description"`

## Useful docs
- [Production Deployment Guide](PRODUCTION_DEPLOYMENT_GUIDE.md) - Complete production setup guide
- [Security Guide](SECURITY_GUIDE.md) - Security implementation and best practices
- [Testing Guide](TESTING_GUIDE.md) - Testing framework and guidelines
- [API Documentation](http://localhost:8000/docs) - Interactive API documentation

## ⚠️ Important

**For database schema changes:**
1. **Removing a column:**
   - Remove the column's usage from the code, and deploy
   - Remove the column from the database schema, and deploy

2. **Adding a column:**
   - Add the column to the database schema, and deploy
   - Add the column's usage to the code, and deploy

**For environment variables:**
- Never commit `.env` files with real secrets
- Use `.env.production` template for production setup
- Generate secure JWT secrets: `python -c "import secrets; print(secrets.token_urlsafe(64))"`

**For logging:**
- Add `[keep-forever]` to critical log entries
- Logs without `[keep-forever]` will be deleted after 30 days

## Health monitoring access commands

**Note:** Please make sure you have configured proper access credentials before executing these commands.

### Application health dashboard
```bash
# Check application health
curl http://localhost:8000/health

# Check database health
curl http://localhost:8000/database/mysql/health

# View application metrics
curl http://localhost:8000/metrics
```

### Database access commands
```bash
# Access MySQL container (development)
docker exec -it my_saas_backend-mysql_dev-1 mysql -u dev_user -p

# Access MySQL container (production)
docker exec -it my_saas_backend-mysql-1 mysql -u saas_user -p

# Database backup
curl -X POST -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8000/database/mysql/backup
```

## Instructions to download database backup
```bash
# Download backup from container
docker cp my_saas_backend-app-1:/app/backups/backup_YYYYMMDD.sql ./local_backup.sql

# Or use API endpoint
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8000/database/mysql/backup/download/backup_YYYYMMDD.sql
```

## Instructions to analyze database data

### Step 1
Install MySQL client locally
```bash
# macOS
brew install mysql-client

# Ubuntu/Debian
sudo apt-get install mysql-client
```

### Step 2
Connect to database directly
```bash
# Development database
mysql -h localhost -P 3306 -u dev_user -p saas_dev_db

# Production database (if accessible)
mysql -h localhost -P 3306 -u saas_user -p saas_prod_db
```

### Step 3
Use database management tools like:
- **MySQL Workbench**: https://www.mysql.com/products/workbench/
- **phpMyAdmin**: For web-based database management
- **DBeaver**: Universal database tool

### Step 4
Connect using the database credentials from your docker-compose files and analyze your data structure and content.