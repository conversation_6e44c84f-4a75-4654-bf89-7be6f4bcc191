"""
Secrets Management Module

This module provides secure handling of sensitive configuration data including:
- Environment variable validation
- Secret rotation capabilities
- Encryption/decryption of sensitive data
- Integration with external secret stores
- Audit logging for secret access
"""

import os
import base64
import hashlib
import secrets
from typing import Dict, Optional, Any, List
from cryptography.fernet import <PERSON><PERSON>t
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class SecretValidationError(Exception):
    """Exception raised when secret validation fails."""
    pass

class SecretsManager:
    """Secure secrets management with encryption and validation."""
    
    def __init__(self):
        self.master_key = self._get_or_create_master_key()
        self.cipher_suite = Fernet(self.master_key)
        self._secret_cache = {}
        self._last_rotation_check = datetime.now()
    
    def _get_or_create_master_key(self) -> bytes:
        """Get or create master encryption key."""
        # In production, this should come from a secure key management service
        master_password = os.getenv("MASTER_SECRET_KEY", "default-master-key-change-in-production")
        
        if master_password == "default-master-key-change-in-production":
            logger.warning("Using default master key. Change MASTER_SECRET_KEY in production!")
        
        # Derive key from password
        salt = b'stable_salt_for_key_derivation'  # In production, use random salt stored securely
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(master_password.encode()))
        return key
    
    def encrypt_secret(self, secret: str) -> str:
        """Encrypt a secret value."""
        try:
            encrypted = self.cipher_suite.encrypt(secret.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Failed to encrypt secret: {e}")
            raise SecretValidationError("Failed to encrypt secret")
    
    def decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt a secret value."""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_secret.encode())
            decrypted = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Failed to decrypt secret: {e}")
            raise SecretValidationError("Failed to decrypt secret")
    
    def validate_secret_strength(self, secret: str, secret_type: str) -> Dict[str, Any]:
        """Validate secret strength based on type."""
        validation_result = {
            "is_valid": True,
            "strength": "unknown",
            "issues": []
        }
        
        if secret_type == "jwt_secret":
            if len(secret) < 32:
                validation_result["issues"].append("JWT secret should be at least 32 characters")
                validation_result["is_valid"] = False
            
            if secret in ["your-secret-key", "secret", "password", "123456"]:
                validation_result["issues"].append("JWT secret is too common/weak")
                validation_result["is_valid"] = False
        
        elif secret_type == "database_password":
            if len(secret) < 12:
                validation_result["issues"].append("Database password should be at least 12 characters")
                validation_result["is_valid"] = False
            
            if not any(c.isupper() for c in secret):
                validation_result["issues"].append("Database password should contain uppercase letters")
                validation_result["is_valid"] = False
            
            if not any(c.islower() for c in secret):
                validation_result["issues"].append("Database password should contain lowercase letters")
                validation_result["is_valid"] = False
            
            if not any(c.isdigit() for c in secret):
                validation_result["issues"].append("Database password should contain numbers")
                validation_result["is_valid"] = False
        
        elif secret_type == "api_key":
            if len(secret) < 20:
                validation_result["issues"].append("API key should be at least 20 characters")
                validation_result["is_valid"] = False
        
        # Determine strength
        if validation_result["is_valid"]:
            if len(secret) >= 64:
                validation_result["strength"] = "strong"
            elif len(secret) >= 32:
                validation_result["strength"] = "medium"
            else:
                validation_result["strength"] = "weak"
        
        return validation_result
    
    def generate_secure_secret(self, length: int = 32) -> str:
        """Generate a cryptographically secure secret."""
        return secrets.token_urlsafe(length)
    
    def hash_secret(self, secret: str) -> str:
        """Create a hash of a secret for comparison."""
        return hashlib.sha256(secret.encode()).hexdigest()

class EnvironmentSecrets:
    """Manage environment-based secrets with validation."""
    
    REQUIRED_SECRETS = {
        "production": [
            "JWT_SECRET_KEY",
            "PROD_DATABASE_URL", 
            "OPENAI_API_KEY",
            "MYSQL_ROOT_PASSWORD",
            "MYSQL_PASSWORD"
        ],
        "development": [
            "JWT_SECRET_KEY",
            "DEV_DATABASE_URL",
            "OPENAI_API_KEY"
        ],
        "test": [
            "JWT_SECRET_KEY",
            "TEST_DATABASE_URL"
        ]
    }
    
    SENSITIVE_PATTERNS = [
        "password", "secret", "key", "token", "credential",
        "private", "auth", "api_key", "access_key"
    ]
    
    def __init__(self):
        self.secrets_manager = SecretsManager()
        self.environment = os.getenv("APP_ENV", "development").lower()
    
    def validate_environment_secrets(self) -> Dict[str, Any]:
        """Validate all required secrets for current environment."""
        validation_result = {
            "environment": self.environment,
            "valid": True,
            "missing_secrets": [],
            "weak_secrets": [],
            "issues": []
        }
        
        required_secrets = self.REQUIRED_SECRETS.get(self.environment, [])
        
        for secret_name in required_secrets:
            secret_value = os.getenv(secret_name)
            
            if not secret_value:
                validation_result["missing_secrets"].append(secret_name)
                validation_result["valid"] = False
                continue
            
            # Check for default/weak values
            if self._is_default_value(secret_name, secret_value):
                validation_result["weak_secrets"].append(secret_name)
                validation_result["issues"].append(f"{secret_name} appears to be a default value")
                validation_result["valid"] = False
            
            # Validate secret strength
            secret_type = self._get_secret_type(secret_name)
            strength_validation = self.secrets_manager.validate_secret_strength(secret_value, secret_type)
            
            if not strength_validation["is_valid"]:
                validation_result["weak_secrets"].append(secret_name)
                validation_result["issues"].extend([
                    f"{secret_name}: {issue}" for issue in strength_validation["issues"]
                ])
                validation_result["valid"] = False
        
        return validation_result
    
    def _is_default_value(self, secret_name: str, secret_value: str) -> bool:
        """Check if secret appears to be a default value."""
        default_patterns = [
            "your-", "change-this", "example", "test", "demo",
            "password", "secret", "key", "123456", "admin"
        ]
        
        secret_lower = secret_value.lower()
        return any(pattern in secret_lower for pattern in default_patterns)
    
    def _get_secret_type(self, secret_name: str) -> str:
        """Determine secret type based on name."""
        name_lower = secret_name.lower()
        
        if "jwt" in name_lower or "secret_key" in name_lower:
            return "jwt_secret"
        elif "database" in name_lower or "mysql" in name_lower:
            return "database_password"
        elif "api_key" in name_lower:
            return "api_key"
        else:
            return "generic"
    
    def get_secret(self, secret_name: str, required: bool = True) -> Optional[str]:
        """Get a secret with validation and logging."""
        secret_value = os.getenv(secret_name)
        
        if not secret_value and required:
            logger.error(f"Required secret {secret_name} not found")
            raise SecretValidationError(f"Required secret {secret_name} not found")
        
        if secret_value and self._is_sensitive_secret(secret_name):
            # Log access to sensitive secrets (without the value)
            logger.info(f"Accessed sensitive secret: {secret_name}")
        
        return secret_value
    
    def _is_sensitive_secret(self, secret_name: str) -> bool:
        """Check if secret is considered sensitive."""
        name_lower = secret_name.lower()
        return any(pattern in name_lower for pattern in self.SENSITIVE_PATTERNS)
    
    def mask_secret(self, secret: str, visible_chars: int = 4) -> str:
        """Mask a secret for logging/display."""
        if len(secret) <= visible_chars * 2:
            return "*" * len(secret)
        
        return secret[:visible_chars] + "*" * (len(secret) - visible_chars * 2) + secret[-visible_chars:]
    
    def rotate_secret(self, secret_name: str, new_secret: Optional[str] = None) -> str:
        """Rotate a secret (generate new value)."""
        if not new_secret:
            # Generate new secret based on type
            secret_type = self._get_secret_type(secret_name)
            if secret_type == "jwt_secret":
                new_secret = self.secrets_manager.generate_secure_secret(64)
            elif secret_type == "database_password":
                new_secret = self.secrets_manager.generate_secure_secret(32)
            else:
                new_secret = self.secrets_manager.generate_secure_secret(32)
        
        # Validate new secret
        secret_type = self._get_secret_type(secret_name)
        validation = self.secrets_manager.validate_secret_strength(new_secret, secret_type)
        
        if not validation["is_valid"]:
            raise SecretValidationError(f"Generated secret is not strong enough: {validation['issues']}")
        
        logger.info(f"Secret rotated: {secret_name}")
        return new_secret

class SecretAuditor:
    """Audit secret access and management."""
    
    def __init__(self):
        self.audit_log = []
    
    def log_secret_access(self, secret_name: str, action: str, user_id: Optional[str] = None, 
                         client_ip: Optional[str] = None):
        """Log secret access for auditing."""
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "secret_name": secret_name,
            "action": action,
            "user_id": user_id,
            "client_ip": client_ip
        }
        
        self.audit_log.append(audit_entry)
        
        # Log to application logger
        logger.info(f"Secret audit: {action} on {secret_name}", extra=audit_entry)
    
    def get_audit_log(self, secret_name: Optional[str] = None, 
                     start_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get audit log entries."""
        filtered_log = self.audit_log
        
        if secret_name:
            filtered_log = [entry for entry in filtered_log if entry["secret_name"] == secret_name]
        
        if start_date:
            filtered_log = [
                entry for entry in filtered_log 
                if datetime.fromisoformat(entry["timestamp"]) >= start_date
            ]
        
        return filtered_log

# Global instances
secrets_manager = SecretsManager()
environment_secrets = EnvironmentSecrets()
secret_auditor = SecretAuditor()

def validate_production_secrets() -> bool:
    """Validate that all production secrets are properly configured."""
    if os.getenv("APP_ENV", "").lower() != "production":
        return True  # Skip validation for non-production environments
    
    validation_result = environment_secrets.validate_environment_secrets()
    
    if not validation_result["valid"]:
        logger.error("Production secrets validation failed:")
        for issue in validation_result["issues"]:
            logger.error(f"  - {issue}")
        
        for missing in validation_result["missing_secrets"]:
            logger.error(f"  - Missing secret: {missing}")
        
        return False
    
    logger.info("Production secrets validation passed")
    return True

def get_secure_config() -> Dict[str, str]:
    """Get secure configuration with masked secrets for logging."""
    config = {}
    
    for key, value in os.environ.items():
        if environment_secrets._is_sensitive_secret(key):
            config[key] = environment_secrets.mask_secret(value)
        else:
            config[key] = value
    
    return config
