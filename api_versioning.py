"""
API Versioning Module

This module provides comprehensive API versioning support including:
- Version-based routing
- Backward compatibility management
- Deprecation warnings
- Version negotiation
- API documentation per version
"""

from typing import Dict, List, Optional, Callable, Any
from fastapi import FastAPI, Request, HTTPException, status, Depends
from fastapi.routing import APIRoute
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import re
from datetime import datetime, timedelta
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class APIVersion(Enum):
    """Supported API versions."""
    V1 = "v1"
    V2 = "v2"  # Future version

class VersionStatus(Enum):
    """API version status."""
    CURRENT = "current"
    SUPPORTED = "supported"
    DEPRECATED = "deprecated"
    SUNSET = "sunset"

class APIVersionInfo:
    """Information about an API version."""
    
    def __init__(
        self,
        version: APIVersion,
        status: VersionStatus,
        release_date: datetime,
        deprecation_date: Optional[datetime] = None,
        sunset_date: Optional[datetime] = None,
        description: str = ""
    ):
        self.version = version
        self.status = status
        self.release_date = release_date
        self.deprecation_date = deprecation_date
        self.sunset_date = sunset_date
        self.description = description
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "version": self.version.value,
            "status": self.status.value,
            "release_date": self.release_date.isoformat(),
            "deprecation_date": self.deprecation_date.isoformat() if self.deprecation_date else None,
            "sunset_date": self.sunset_date.isoformat() if self.sunset_date else None,
            "description": self.description
        }

class APIVersionManager:
    """Manage API versions and routing."""
    
    def __init__(self):
        self.versions: Dict[APIVersion, APIVersionInfo] = {}
        self.default_version = APIVersion.V1
        self.current_version = APIVersion.V1
        self._setup_versions()
    
    def _setup_versions(self):
        """Set up supported API versions."""
        # Version 1 - Current
        self.versions[APIVersion.V1] = APIVersionInfo(
            version=APIVersion.V1,
            status=VersionStatus.CURRENT,
            release_date=datetime(2024, 1, 1),
            description="Initial API version with core functionality"
        )
        
        # Version 2 - Future (example)
        # self.versions[APIVersion.V2] = APIVersionInfo(
        #     version=APIVersion.V2,
        #     status=VersionStatus.SUPPORTED,
        #     release_date=datetime(2024, 6, 1),
        #     description="Enhanced API with improved authentication and new features"
        # )
    
    def get_version_info(self, version: APIVersion) -> Optional[APIVersionInfo]:
        """Get information about a specific version."""
        return self.versions.get(version)
    
    def get_all_versions(self) -> List[APIVersionInfo]:
        """Get information about all versions."""
        return list(self.versions.values())
    
    def is_version_supported(self, version: APIVersion) -> bool:
        """Check if a version is supported."""
        version_info = self.versions.get(version)
        if not version_info:
            return False
        
        return version_info.status in [VersionStatus.CURRENT, VersionStatus.SUPPORTED]
    
    def is_version_deprecated(self, version: APIVersion) -> bool:
        """Check if a version is deprecated."""
        version_info = self.versions.get(version)
        if not version_info:
            return False
        
        return version_info.status == VersionStatus.DEPRECATED
    
    def get_deprecation_warning(self, version: APIVersion) -> Optional[str]:
        """Get deprecation warning for a version."""
        version_info = self.versions.get(version)
        if not version_info or version_info.status != VersionStatus.DEPRECATED:
            return None
        
        warning = f"API version {version.value} is deprecated"
        
        if version_info.sunset_date:
            warning += f" and will be sunset on {version_info.sunset_date.strftime('%Y-%m-%d')}"
        
        warning += f". Please migrate to version {self.current_version.value}."
        
        return warning

class VersionExtractor:
    """Extract API version from requests."""
    
    @staticmethod
    def from_header(request: Request) -> Optional[APIVersion]:
        """Extract version from Accept header."""
        accept_header = request.headers.get("accept", "")
        
        # Look for application/vnd.api+json;version=v1
        version_match = re.search(r'version=([^;,\s]+)', accept_header)
        if version_match:
            version_str = version_match.group(1)
            try:
                return APIVersion(version_str)
            except ValueError:
                return None
        
        return None
    
    @staticmethod
    def from_path(request: Request) -> Optional[APIVersion]:
        """Extract version from URL path."""
        path = request.url.path
        
        # Look for /api/v1/ pattern
        version_match = re.search(r'/api/(v\d+)/', path)
        if version_match:
            version_str = version_match.group(1)
            try:
                return APIVersion(version_str)
            except ValueError:
                return None
        
        return None
    
    @staticmethod
    def from_query_param(request: Request) -> Optional[APIVersion]:
        """Extract version from query parameter."""
        version_param = request.query_params.get("version")
        if version_param:
            try:
                return APIVersion(version_param)
            except ValueError:
                return None
        
        return None

class APIVersionMiddleware(BaseHTTPMiddleware):
    """Middleware for API version handling."""

    def __init__(self, app: FastAPI, version_manager: APIVersionManager):
        super().__init__(app)
        self.version_manager = version_manager

    async def dispatch(self, request: Request, call_next):
        """Process request with version handling."""
        # Extract version from request
        version = self._extract_version(request)
        
        # Set default version if none specified
        if not version:
            version = self.version_manager.default_version
        
        # Validate version
        if not self.version_manager.is_version_supported(version):
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "error": "Unsupported API version",
                    "requested_version": version.value if version else None,
                    "supported_versions": [v.version.value for v in self.version_manager.get_all_versions()]
                }
            )
        
        # Add version to request state
        request.state.api_version = version
        
        # Process request
        response = await call_next(request)
        
        # Add version headers to response
        response.headers["API-Version"] = version.value
        response.headers["API-Supported-Versions"] = ",".join([
            v.version.value for v in self.version_manager.get_all_versions()
        ])
        
        # Add deprecation warning if needed
        if self.version_manager.is_version_deprecated(version):
            warning = self.version_manager.get_deprecation_warning(version)
            if warning:
                response.headers["Warning"] = f'299 - "{warning}"'
                response.headers["Deprecation"] = "true"
        
        return response
    
    def _extract_version(self, request: Request) -> Optional[APIVersion]:
        """Extract version from request using multiple methods."""
        # Try path first (most explicit)
        version = VersionExtractor.from_path(request)
        if version:
            return version
        
        # Try header
        version = VersionExtractor.from_header(request)
        if version:
            return version
        
        # Try query parameter
        version = VersionExtractor.from_query_param(request)
        if version:
            return version
        
        return None

def get_api_version(request: Request) -> APIVersion:
    """Dependency to get current API version."""
    return getattr(request.state, 'api_version', APIVersion.V1)

def require_version(required_version: APIVersion):
    """Decorator to require specific API version."""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            # This would be used with FastAPI dependencies
            # The actual implementation would check request.state.api_version
            return func(*args, **kwargs)
        return wrapper
    return decorator

class VersionedAPIRoute(APIRoute):
    """Custom API route with version support."""
    
    def __init__(
        self,
        path: str,
        endpoint: Callable,
        *,
        min_version: Optional[APIVersion] = None,
        max_version: Optional[APIVersion] = None,
        deprecated_in: Optional[APIVersion] = None,
        **kwargs
    ):
        self.min_version = min_version
        self.max_version = max_version
        self.deprecated_in = deprecated_in
        super().__init__(path, endpoint, **kwargs)
    
    def matches(self, scope: Dict[str, Any]) -> tuple:
        """Check if route matches request with version consideration."""
        match, child_scope = super().matches(scope)
        
        if not match:
            return match, child_scope
        
        # Additional version-based matching could be implemented here
        return match, child_scope

class APIVersionResponse:
    """Standardized API version response format."""
    
    @staticmethod
    def success(data: Any, version: APIVersion, message: str = "Success") -> Dict[str, Any]:
        """Create successful response with version info."""
        return {
            "success": True,
            "message": message,
            "data": data,
            "api_version": version.value,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def error(error: str, version: APIVersion, code: str = "ERROR") -> Dict[str, Any]:
        """Create error response with version info."""
        return {
            "success": False,
            "error": error,
            "code": code,
            "api_version": version.value,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def paginated(
        data: List[Any],
        page: int,
        per_page: int,
        total: int,
        version: APIVersion
    ) -> Dict[str, Any]:
        """Create paginated response with version info."""
        return {
            "success": True,
            "data": data,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": (total + per_page - 1) // per_page
            },
            "api_version": version.value,
            "timestamp": datetime.now().isoformat()
        }

# Global version manager instance
version_manager = APIVersionManager()

def setup_api_versioning(app: FastAPI):
    """Set up API versioning for FastAPI app."""
    # Add version middleware
    app.add_middleware(APIVersionMiddleware, version_manager=version_manager)
    
    # Add version info endpoint
    @app.get("/api/versions")
    async def get_api_versions():
        """Get information about all API versions."""
        versions = [v.to_dict() for v in version_manager.get_all_versions()]
        return APIVersionResponse.success(
            data={"versions": versions},
            version=version_manager.current_version,
            message="API version information"
        )
    
    # Add current version endpoint
    @app.get("/api/version")
    async def get_current_version(version: APIVersion = Depends(get_api_version)):
        """Get current API version."""
        version_info = version_manager.get_version_info(version)
        return APIVersionResponse.success(
            data=version_info.to_dict() if version_info else None,
            version=version,
            message="Current API version"
        )

def create_versioned_router(version: APIVersion, prefix: str = ""):
    """Create a router for a specific API version."""
    from fastapi import APIRouter
    
    router = APIRouter(
        prefix=f"/api/{version.value}{prefix}",
        tags=[f"API {version.value}"]
    )
    
    return router
