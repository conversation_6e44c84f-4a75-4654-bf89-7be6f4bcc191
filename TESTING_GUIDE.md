# Testing Guide for SaaS Backend

This guide provides comprehensive information about testing the SaaS Backend application.

## 🧪 **Testing Overview**

The testing infrastructure includes:
- **Unit Tests**: Fast, isolated tests for individual components
- **Integration Tests**: Tests for component interactions and database operations
- **API Tests**: End-to-end tests for API endpoints
- **Performance Tests**: Tests for performance characteristics
- **Security Tests**: Tests for security vulnerabilities

## 📁 **Test Structure**

```
tests/
├── __init__.py
├── conftest.py              # Shared fixtures and configuration
├── unit/                    # Unit tests
│   ├── __init__.py
│   ├── test_auth.py        # Authentication module tests
│   └── test_models.py      # Database model tests
├── integration/             # Integration tests
│   ├── __init__.py
│   └── test_database_operations.py
├── api/                     # API endpoint tests
│   ├── __init__.py
│   └── test_auth_endpoints.py
└── fixtures/                # Test data fixtures
    └── __init__.py
```

## 🚀 **Quick Start**

### 1. Install Test Dependencies
```bash
# Install all dependencies including test packages
pip install -r requirements.txt

# Or install test dependencies specifically
pip install pytest pytest-asyncio pytest-cov pytest-mock httpx factory-boy faker
```

### 2. Set Up Test Database
```bash
# Set up test database
python setup_mysql_all_envs.py --env test

# Or manually create test database
mysql -u root -p -e "CREATE DATABASE saas_test_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 3. Run Tests
```bash
# Run all tests
python run_tests.py --type all

# Run specific test types
python run_tests.py --type unit
python run_tests.py --type integration
python run_tests.py --type api

# Run with coverage
python run_tests.py --type all --coverage

# Run verbose
python run_tests.py --type all --verbose
```

## 🔧 **Test Configuration**

### Environment Variables
Tests use the following environment variables:
```bash
APP_ENV=test
TEST_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_test_db
JWT_SECRET_KEY=test-jwt-secret-key-for-testing-only
OPENAI_API_KEY=test-openai-key
LOG_LEVEL=WARNING
ENABLE_RATE_LIMITING=false
SQL_ECHO=false
```

### Pytest Configuration
Configuration is in `pytest.ini`:
- Test discovery patterns
- Coverage settings
- Test markers
- Async support

## 📊 **Test Categories**

### Unit Tests (`@pytest.mark.unit`)
Fast, isolated tests that don't require external dependencies:
- Authentication utilities
- Password management
- JWT token handling
- Model validation
- Utility functions

**Example:**
```bash
python run_tests.py --type unit
```

### Integration Tests (`@pytest.mark.integration`)
Tests that involve multiple components:
- Database operations
- MySQL manager functionality
- Health check endpoints
- Transaction handling

**Example:**
```bash
python run_tests.py --type integration
```

### API Tests (`@pytest.mark.api`)
End-to-end tests for API endpoints:
- Authentication endpoints
- Protected routes
- Request/response validation
- Error handling

**Example:**
```bash
python run_tests.py --type api
```

### Database Tests (`@pytest.mark.database`)
Tests that require database connectivity:
- Model CRUD operations
- Database constraints
- Relationship testing
- Performance testing

**Example:**
```bash
python run_tests.py --type database
```

### Authentication Tests (`@pytest.mark.auth`)
Tests for authentication and authorization:
- User registration/login
- Token management
- Protected endpoint access
- Permission checking

**Example:**
```bash
python run_tests.py --type auth
```

### AI Tests (`@pytest.mark.ai`)
Tests for AI integration:
- OpenAI API integration
- Message classification
- SQL generation
- Fallback mechanisms

**Example:**
```bash
python run_tests.py --type ai
```

## 🎯 **Test Fixtures**

### Database Fixtures
- `db_session`: Fresh database session for each test
- `test_engine`: Test database engine
- `test_session_factory`: Session factory for tests

### Authentication Fixtures
- `test_user_data`: Sample user data
- `created_user`: User created in database
- `user_token`: JWT token for user
- `auth_headers`: Authorization headers

### Mock Fixtures
- `mock_openai`: Mocked OpenAI API
- `mock_mysql_manager`: Mocked MySQL manager

### Data Fixtures
- `sample_tenant_data`: Sample tenant information
- `sample_message_data`: Sample AI message data

## 🔍 **Running Specific Tests**

### By Test File
```bash
# Run specific test file
pytest tests/unit/test_auth.py

# Run specific test class
pytest tests/unit/test_auth.py::TestPasswordManager

# Run specific test method
pytest tests/unit/test_auth.py::TestPasswordManager::test_hash_password
```

### By Markers
```bash
# Run only unit tests
pytest -m unit

# Run only database tests
pytest -m database

# Run only fast tests (exclude slow tests)
pytest -m "not slow"

# Combine markers
pytest -m "unit and auth"
```

### By Pattern
```bash
# Run tests matching pattern
pytest -k "test_auth"

# Run tests NOT matching pattern
pytest -k "not test_performance"
```

## 📈 **Coverage Reports**

### Generate Coverage
```bash
# Run tests with coverage
python run_tests.py --type all --coverage

# Or directly with pytest
pytest --cov=. --cov-report=html --cov-report=term-missing
```

### View Coverage
```bash
# Terminal report
pytest --cov=. --cov-report=term-missing

# HTML report (opens in browser)
pytest --cov=. --cov-report=html
open htmlcov/index.html

# XML report (for CI/CD)
pytest --cov=. --cov-report=xml
```

## 🚨 **Troubleshooting**

### Common Issues

#### 1. Database Connection Errors
```bash
# Check if MySQL is running
sudo systemctl status mysql

# Check if test database exists
mysql -u root -p -e "SHOW DATABASES LIKE 'saas_test_db';"

# Create test database
python setup_mysql_all_envs.py --env test
```

#### 2. Import Errors
```bash
# Install missing dependencies
pip install -r requirements.txt

# Check Python path
python -c "import sys; print(sys.path)"

# Run from project root
cd /path/to/My_SaaS_Backend
python run_tests.py
```

#### 3. Permission Errors
```bash
# Check file permissions
ls -la run_tests.py

# Make executable
chmod +x run_tests.py

# Check database permissions
mysql -u root -p -e "SHOW GRANTS FOR 'root'@'localhost';"
```

#### 4. Test Failures
```bash
# Run with verbose output
python run_tests.py --type all --verbose

# Run specific failing test
pytest tests/path/to/test.py::test_name -v

# Debug with pdb
pytest tests/path/to/test.py::test_name --pdb
```

## 🔄 **Continuous Integration**

### GitHub Actions Example
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: saas_test_db
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.11
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: python run_tests.py --type all --coverage
```

## 📝 **Writing New Tests**

### Test Naming Convention
- Test files: `test_*.py`
- Test classes: `Test*`
- Test methods: `test_*`

### Example Test
```python
import pytest

class TestMyFeature:
    @pytest.mark.unit
    def test_my_function_success(self):
        """Test successful case."""
        result = my_function("valid_input")
        assert result == "expected_output"
    
    @pytest.mark.unit
    def test_my_function_error(self):
        """Test error case."""
        with pytest.raises(ValueError):
            my_function("invalid_input")
```

### Best Practices
1. **Use descriptive test names**
2. **Test both success and failure cases**
3. **Use appropriate markers**
4. **Keep tests independent**
5. **Use fixtures for common setup**
6. **Mock external dependencies**
7. **Test edge cases**

## 📊 **Test Metrics**

### Current Coverage Targets
- **Overall Coverage**: 80%+
- **Unit Tests**: 90%+
- **Critical Paths**: 95%+

### Performance Targets
- **Unit Tests**: < 0.1s per test
- **Integration Tests**: < 1s per test
- **API Tests**: < 2s per test

## 🎯 **Next Steps**

1. **Increase Coverage**: Add tests for uncovered code
2. **Performance Tests**: Add more performance benchmarks
3. **Security Tests**: Add security vulnerability tests
4. **Load Tests**: Add load testing with locust
5. **E2E Tests**: Add end-to-end browser tests
