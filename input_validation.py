"""
Advanced Input Validation and Sanitization Module

This module provides comprehensive input validation, sanitization,
and protection against common security vulnerabilities including:
- SQL injection prevention
- XSS protection
- Command injection prevention
- Path traversal protection
- Input size limits
- Content type validation
"""

import re
import html
import urllib.parse
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, validator, Field
from fastapi import HTTPException, status
import logging
import bleach
from pathlib import Path

logger = logging.getLogger(__name__)

class SecurityConfig:
    """Security configuration constants."""
    
    # Input size limits
    MAX_STRING_LENGTH = 10000
    MAX_TEXT_LENGTH = 50000
    MAX_EMAIL_LENGTH = 254
    MAX_NAME_LENGTH = 100
    MAX_PHONE_LENGTH = 20
    MAX_ADDRESS_LENGTH = 500
    MAX_QUERY_LENGTH = 5000
    
    # SQL injection patterns
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
        r"(\b(UNION|OR|AND)\s+\d+\s*=\s*\d+)",
        r"(--|#|/\*|\*/)",
        r"(\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR)\b)",
        r"([\'\";])",
        r"(\b(WAITFOR|DELAY|SLEEP)\b)",
        r"(\b(XP_|SP_)\w+)",
        r"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)"
    ]
    
    # XSS patterns
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"vbscript:",
        r"onload\s*=",
        r"onerror\s*=",
        r"onclick\s*=",
        r"onmouseover\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
        r"<link[^>]*>",
        r"<meta[^>]*>"
    ]
    
    # Command injection patterns
    COMMAND_INJECTION_PATTERNS = [
        r"[;&|`$(){}[\]\\]",
        r"\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl)\b",
        r"(\.\.\/|\.\.\\)",
        r"(\$\{|\$\()",
        r"(\|\s*(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl))"
    ]
    
    # Allowed HTML tags for rich text (if needed)
    ALLOWED_HTML_TAGS = ['b', 'i', 'u', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li']
    ALLOWED_HTML_ATTRIBUTES = {}

class InputSanitizer:
    """Advanced input sanitization utilities."""
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = SecurityConfig.MAX_STRING_LENGTH) -> str:
        """Sanitize a general string input."""
        if not isinstance(value, str):
            raise ValueError("Input must be a string")
        
        # Trim whitespace
        value = value.strip()
        
        # Check length
        if len(value) > max_length:
            raise ValueError(f"Input too long. Maximum {max_length} characters allowed")
        
        # HTML encode to prevent XSS
        value = html.escape(value)
        
        # URL decode to normalize input
        value = urllib.parse.unquote(value)
        
        return value
    
    @staticmethod
    def sanitize_html(value: str, max_length: int = SecurityConfig.MAX_TEXT_LENGTH) -> str:
        """Sanitize HTML content allowing only safe tags."""
        if not isinstance(value, str):
            raise ValueError("Input must be a string")
        
        if len(value) > max_length:
            raise ValueError(f"HTML content too long. Maximum {max_length} characters allowed")
        
        # Use bleach to sanitize HTML
        cleaned = bleach.clean(
            value,
            tags=SecurityConfig.ALLOWED_HTML_TAGS,
            attributes=SecurityConfig.ALLOWED_HTML_ATTRIBUTES,
            strip=True
        )
        
        return cleaned
    
    @staticmethod
    def sanitize_sql_input(value: str) -> str:
        """Sanitize input that might be used in SQL contexts."""
        if not isinstance(value, str):
            raise ValueError("Input must be a string")
        
        # Check for SQL injection patterns
        for pattern in SecurityConfig.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential SQL injection attempt detected: {pattern}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid input detected. Please check your input and try again."
                )
        
        # Additional sanitization
        value = value.replace("'", "''")  # Escape single quotes
        value = re.sub(r'[^\w\s@.-]', '', value)  # Remove special characters except safe ones
        
        return value
    
    @staticmethod
    def detect_xss(value: str) -> bool:
        """Detect potential XSS attempts."""
        if not isinstance(value, str):
            return False
        
        for pattern in SecurityConfig.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential XSS attempt detected: {pattern}")
                return True
        
        return False
    
    @staticmethod
    def detect_command_injection(value: str) -> bool:
        """Detect potential command injection attempts."""
        if not isinstance(value, str):
            return False
        
        for pattern in SecurityConfig.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential command injection attempt detected: {pattern}")
                return True
        
        return False
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename to prevent path traversal."""
        if not isinstance(filename, str):
            raise ValueError("Filename must be a string")
        
        # Remove path components
        filename = Path(filename).name
        
        # Remove dangerous characters
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        
        # Limit length
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:250] + ('.' + ext if ext else '')
        
        # Prevent empty filename
        if not filename or filename in ['.', '..']:
            filename = 'file'
        
        return filename

class SecureBaseModel(BaseModel):
    """Base model with built-in security validation."""
    
    class Config:
        # Validate assignment to prevent injection after creation
        validate_assignment = True
        # Use enum values for better validation
        use_enum_values = True
        # Forbid extra fields to prevent injection
        extra = "forbid"

class SecureStringField(str):
    """Custom string field with security validation."""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v, field=None):
        if not isinstance(v, str):
            raise TypeError('string required')
        
        # Get max length from field info
        max_length = getattr(field, 'max_length', SecurityConfig.MAX_STRING_LENGTH)
        
        # Sanitize input
        sanitized = InputSanitizer.sanitize_string(v, max_length)
        
        # Check for XSS
        if InputSanitizer.detect_xss(sanitized):
            raise ValueError('Potentially malicious content detected')
        
        # Check for command injection
        if InputSanitizer.detect_command_injection(sanitized):
            raise ValueError('Potentially malicious content detected')
        
        return sanitized

class SecureEmailField(str):
    """Custom email field with security validation."""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        
        # Length check
        if len(v) > SecurityConfig.MAX_EMAIL_LENGTH:
            raise ValueError(f'Email too long. Maximum {SecurityConfig.MAX_EMAIL_LENGTH} characters')
        
        # Sanitize
        sanitized = InputSanitizer.sanitize_string(v, SecurityConfig.MAX_EMAIL_LENGTH)
        
        return sanitized.lower()

class SecureQueryField(str):
    """Custom field for database queries with SQL injection protection."""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError('string required')
        
        # Length check
        if len(v) > SecurityConfig.MAX_QUERY_LENGTH:
            raise ValueError(f'Query too long. Maximum {SecurityConfig.MAX_QUERY_LENGTH} characters')
        
        # SQL injection check
        sanitized = InputSanitizer.sanitize_sql_input(v)
        
        return sanitized

# Secure Pydantic models for API endpoints
class SecureUserRegistration(SecureBaseModel):
    """Secure user registration model."""
    email: SecureEmailField
    password: str = Field(..., min_length=8, max_length=128)
    full_name: Optional[SecureStringField] = Field(None, max_length=SecurityConfig.MAX_NAME_LENGTH)
    
    @validator('password')
    def validate_password(cls, v):
        from auth import PasswordManager
        validation = PasswordManager.validate_password_strength(v)
        if not validation["is_valid"]:
            raise ValueError(f"Password requirements not met: {', '.join(validation['errors'])}")
        return v

class SecureUserLogin(SecureBaseModel):
    """Secure user login model."""
    email: SecureEmailField
    password: str = Field(..., min_length=1, max_length=128)

class SecureDatabaseQuery(SecureBaseModel):
    """Secure database query model."""
    query: SecureQueryField = Field(..., min_length=1)
    selected_tables: List[str] = Field(default_factory=list, max_items=50)
    
    @validator('selected_tables')
    def validate_table_names(cls, v):
        if not isinstance(v, list):
            raise TypeError('selected_tables must be a list')
        
        sanitized_tables = []
        for table in v:
            if not isinstance(table, str):
                raise TypeError('Table names must be strings')
            
            # Sanitize table name
            table = re.sub(r'[^\w_]', '', table)  # Only allow alphanumeric and underscore
            if len(table) > 64:  # MySQL table name limit
                raise ValueError('Table name too long')
            
            if table:  # Only add non-empty names
                sanitized_tables.append(table)
        
        return sanitized_tables

class SecureTenantData(SecureBaseModel):
    """Secure tenant data model."""
    employer_name: SecureStringField = Field(..., max_length=SecurityConfig.MAX_NAME_LENGTH)
    email: SecureEmailField
    phone: Optional[SecureStringField] = Field(None, max_length=SecurityConfig.MAX_PHONE_LENGTH)
    address: Optional[SecureStringField] = Field(None, max_length=SecurityConfig.MAX_ADDRESS_LENGTH)
    
    @validator('phone')
    def validate_phone(cls, v):
        if v is None:
            return v
        
        # Remove all non-digit characters except + and -
        phone = re.sub(r'[^\d+\-\s()]', '', v)
        
        # Basic phone validation
        if len(phone) < 10 or len(phone) > 20:
            raise ValueError('Invalid phone number format')
        
        return phone

class SecurityValidator:
    """Security validation utilities for API endpoints."""
    
    @staticmethod
    def validate_request_size(content_length: Optional[int], max_size: int = 10 * 1024 * 1024):
        """Validate request content size."""
        if content_length and content_length > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"Request too large. Maximum size: {max_size} bytes"
            )
    
    @staticmethod
    def validate_content_type(content_type: Optional[str], allowed_types: List[str]):
        """Validate request content type."""
        if content_type and content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                detail=f"Unsupported content type. Allowed: {', '.join(allowed_types)}"
            )
    
    @staticmethod
    def validate_user_agent(user_agent: Optional[str]):
        """Validate and log suspicious user agents."""
        if not user_agent:
            logger.warning("Request without User-Agent header")
            return
        
        # Check for suspicious patterns
        suspicious_patterns = [
            r'sqlmap', r'nikto', r'nmap', r'masscan', r'zap',
            r'burp', r'w3af', r'acunetix', r'nessus'
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, user_agent, re.IGNORECASE):
                logger.warning(f"Suspicious User-Agent detected: {user_agent}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
    
    @staticmethod
    def log_security_event(event_type: str, details: Dict[str, Any], client_ip: str):
        """Log security events for monitoring."""
        logger.warning(f"Security Event: {event_type}", extra={
            "event_type": event_type,
            "client_ip": client_ip,
            "details": details,
            "timestamp": "now"
        })
