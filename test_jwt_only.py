#!/usr/bin/env python3
"""
Simple test script to check if JWT import works.
Run this after installing PyJWT to verify the fix.
"""

def test_jwt_import():
    """Test JWT import and basic functionality."""
    print("🧪 Testing JWT import...")
    
    try:
        import jwt
        print("✅ JWT import successful")
        
        # Test basic JWT functionality
        test_payload = {"user_id": "123", "email": "<EMAIL>"}
        test_secret = "test-secret-key"
        
        # Encode a token
        token = jwt.encode(test_payload, test_secret, algorithm="HS256")
        print(f"✅ JWT encode successful: {token[:20]}...")
        
        # Decode the token
        decoded = jwt.decode(token, test_secret, algorithms=["HS256"])
        print(f"✅ JWT decode successful: {decoded}")
        
        if decoded == test_payload:
            print("✅ JWT functionality test PASSED")
            return True
        else:
            print("❌ JWT functionality test FAILED")
            return False
            
    except ImportError as e:
        print(f"❌ JWT import FAILED: {e}")
        print("\n💡 To fix this, run:")
        print("pip3 install PyJWT>=2.8.0")
        return False
    except Exception as e:
        print(f"❌ JWT test FAILED: {e}")
        return False

def test_auth_imports():
    """Test other authentication imports."""
    print("\n🧪 Testing other auth imports...")
    
    imports = [
        ("passlib.context", "CryptContext"),
        ("bcrypt", None),
    ]
    
    for module, attr in imports:
        try:
            if attr:
                exec(f"from {module} import {attr}")
                print(f"✅ {module}.{attr}")
            else:
                exec(f"import {module}")
                print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")

if __name__ == "__main__":
    print("🚀 JWT Test Script")
    print("=" * 40)
    
    jwt_success = test_jwt_import()
    test_auth_imports()
    
    print("\n" + "=" * 40)
    if jwt_success:
        print("🎉 JWT is working! You can now run your application.")
        print("\nNext steps:")
        print("1. Run: python3 main.py")
        print("2. Or: uvicorn main:app --reload")
        print("3. Or: docker-compose -f docker-compose.simple.yml up --build")
    else:
        print("❌ JWT is not working. Please install it first:")
        print("pip3 install PyJWT>=2.8.0")
