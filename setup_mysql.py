#!/usr/bin/env python3
"""
MySQL Database Setup Script

This script helps set up MySQL database for production deployment.
It handles database creation, user setup, and initial configuration.
"""

import os
import sys
import getpass
import pymysql
from dotenv import load_dotenv
from mysql_manager import MySQLManager
import argparse

# Load environment variables
load_dotenv()

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"🗄️  {title}")
    print('='*60)

def print_step(step, description):
    """Print a step description."""
    print(f"\n{step}. {description}")

def get_mysql_credentials():
    """Get MySQL credentials from user input."""
    print_section("MySQL Database Setup")
    print("Please provide MySQL connection details:")
    
    host = input("MySQL Host [localhost]: ").strip() or "localhost"
    port = input("MySQL Port [3306]: ").strip() or "3306"
    
    print("\nRoot MySQL credentials (for database creation):")
    root_user = input("Root Username [root]: ").strip() or "root"
    root_password = getpass.getpass("Root Password: ")
    
    print("\nApplication database details:")
    db_name = input("Database Name [saas_prod_db]: ").strip() or "saas_prod_db"
    
    print("\nApplication user credentials:")
    app_user = input("Application Username [saas_user]: ").strip() or "saas_user"
    app_password = getpass.getpass("Application Password: ")
    
    return {
        "host": host,
        "port": int(port),
        "root_user": root_user,
        "root_password": root_password,
        "db_name": db_name,
        "app_user": app_user,
        "app_password": app_password
    }

def test_root_connection(credentials):
    """Test connection with root credentials."""
    print_step(1, "Testing root MySQL connection...")
    
    try:
        connection = pymysql.connect(
            host=credentials["host"],
            port=credentials["port"],
            user=credentials["root_user"],
            password=credentials["root_password"]
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"✅ Connected to MySQL {version}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Root connection failed: {e}")
        return False

def create_database(credentials):
    """Create the application database."""
    print_step(2, f"Creating database '{credentials['db_name']}'...")
    
    try:
        connection = pymysql.connect(
            host=credentials["host"],
            port=credentials["port"],
            user=credentials["root_user"],
            password=credentials["root_password"]
        )
        
        with connection.cursor() as cursor:
            # Check if database exists
            cursor.execute(f"SHOW DATABASES LIKE '{credentials['db_name']}'")
            if cursor.fetchone():
                print(f"⚠️  Database '{credentials['db_name']}' already exists")
                return True
            
            # Create database
            cursor.execute(f"""
                CREATE DATABASE {credentials['db_name']} 
                CHARACTER SET utf8mb4 
                COLLATE utf8mb4_unicode_ci
            """)
            print(f"✅ Database '{credentials['db_name']}' created successfully")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Database creation failed: {e}")
        return False

def create_user(credentials):
    """Create the application user."""
    print_step(3, f"Creating application user '{credentials['app_user']}'...")
    
    try:
        connection = pymysql.connect(
            host=credentials["host"],
            port=credentials["port"],
            user=credentials["root_user"],
            password=credentials["root_password"]
        )
        
        with connection.cursor() as cursor:
            # Check if user exists
            cursor.execute(f"SELECT User FROM mysql.user WHERE User = '{credentials['app_user']}'")
            if cursor.fetchone():
                print(f"⚠️  User '{credentials['app_user']}' already exists")
                # Update password
                cursor.execute(f"""
                    ALTER USER '{credentials['app_user']}'@'%' 
                    IDENTIFIED BY '{credentials['app_password']}'
                """)
            else:
                # Create user
                cursor.execute(f"""
                    CREATE USER '{credentials['app_user']}'@'%' 
                    IDENTIFIED BY '{credentials['app_password']}'
                """)
                print(f"✅ User '{credentials['app_user']}' created successfully")
            
            # Grant privileges
            cursor.execute(f"""
                GRANT ALL PRIVILEGES ON {credentials['db_name']}.* 
                TO '{credentials['app_user']}'@'%'
            """)
            
            # Grant connection privileges
            cursor.execute(f"""
                GRANT PROCESS, REPLICATION CLIENT ON *.* 
                TO '{credentials['app_user']}'@'%'
            """)
            
            cursor.execute("FLUSH PRIVILEGES")
            print(f"✅ Privileges granted to '{credentials['app_user']}'")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ User creation failed: {e}")
        return False

def test_app_connection(credentials):
    """Test connection with application credentials."""
    print_step(4, "Testing application user connection...")
    
    try:
        connection = pymysql.connect(
            host=credentials["host"],
            port=credentials["port"],
            user=credentials["app_user"],
            password=credentials["app_password"],
            database=credentials["db_name"]
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()[0]
            print(f"✅ Application user can connect to database: {current_db}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Application connection failed: {e}")
        return False

def generate_env_config(credentials):
    """Generate environment configuration."""
    print_step(5, "Generating environment configuration...")
    
    database_url = (
        f"mysql+pymysql://{credentials['app_user']}:{credentials['app_password']}"
        f"@{credentials['host']}:{credentials['port']}/{credentials['db_name']}"
    )
    
    env_config = f"""
# MySQL Production Database Configuration
PROD_DATABASE_URL={database_url}
APP_ENV=production

# Database Pool Settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=10
DB_READ_TIMEOUT=30
DB_WRITE_TIMEOUT=30
"""
    
    print("✅ Environment configuration generated:")
    print(env_config)
    
    # Save to file
    with open(".env.mysql", "w") as f:
        f.write(env_config.strip())
    
    print("💾 Configuration saved to .env.mysql")
    print("📝 Add these settings to your .env file for production")

def test_mysql_manager(credentials):
    """Test the MySQL manager with new configuration."""
    print_step(6, "Testing MySQL manager...")
    
    database_url = (
        f"mysql+pymysql://{credentials['app_user']}:{credentials['app_password']}"
        f"@{credentials['host']}:{credentials['port']}/{credentials['db_name']}"
    )
    
    try:
        manager = MySQLManager(database_url)
        
        # Test connection
        connection_test = manager.test_connection()
        if connection_test["status"] == "success":
            print(f"✅ MySQL Manager connection successful")
            print(f"   MySQL Version: {connection_test['mysql_version']}")
            print(f"   Current Database: {connection_test['current_database']}")
        else:
            print(f"❌ MySQL Manager test failed: {connection_test.get('error')}")
            return False
        
        # Get database info
        db_info = manager.get_database_info()
        if "error" not in db_info:
            print(f"✅ Database info retrieved successfully")
            print(f"   Database: {db_info['database_name']}")
            print(f"   Tables: {db_info['table_count']}")
            print(f"   Size: {db_info['size_mb']} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ MySQL Manager test failed: {e}")
        return False

def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="MySQL Database Setup for SaaS Backend")
    parser.add_argument("--auto", action="store_true", help="Use environment variables for setup")
    args = parser.parse_args()
    
    if args.auto:
        # Use environment variables
        credentials = {
            "host": os.getenv("MYSQL_HOST", "localhost"),
            "port": int(os.getenv("MYSQL_PORT", "3306")),
            "root_user": os.getenv("MYSQL_ROOT_USER", "root"),
            "root_password": os.getenv("MYSQL_ROOT_PASSWORD", ""),
            "db_name": os.getenv("MYSQL_DB_NAME", "saas_prod_db"),
            "app_user": os.getenv("MYSQL_APP_USER", "saas_user"),
            "app_password": os.getenv("MYSQL_APP_PASSWORD", "")
        }
        
        if not credentials["root_password"] or not credentials["app_password"]:
            print("❌ Missing required passwords in environment variables")
            return 1
    else:
        # Interactive setup
        credentials = get_mysql_credentials()
    
    # Setup steps
    steps = [
        ("Test root connection", test_root_connection),
        ("Create database", create_database),
        ("Create user", create_user),
        ("Test app connection", test_app_connection),
        ("Generate config", generate_env_config),
        ("Test MySQL manager", test_mysql_manager)
    ]
    
    print_section("Starting MySQL Setup Process")
    
    for step_name, step_func in steps:
        if not step_func(credentials):
            print(f"\n❌ Setup failed at step: {step_name}")
            return 1
    
    print_section("Setup Complete!")
    print("🎉 MySQL database setup completed successfully!")
    print("\nNext steps:")
    print("1. Update your .env file with the generated configuration")
    print("2. Set APP_ENV=production in your environment")
    print("3. Restart your application")
    print("4. Test the /database/mysql/health endpoint")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
