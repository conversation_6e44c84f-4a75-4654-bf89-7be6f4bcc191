"""
AI Context Enhancement Service

This service provides rich context to the AI Assistant about tenant databases,
including schema information, business context, and learning from past interactions.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from database import get_db_session
from models import (
    DatabaseConnection, DatabaseTable, DatabaseColumn, TableRelationship,
    TenantAIProfile, AITrainingFeedback, QueryHistory, TenantUser
)

logger = logging.getLogger(__name__)


class AIContextService:
    """Service for providing enhanced context to AI Assistant."""
    
    def __init__(self):
        pass
    
    def get_enhanced_context(self, database_connection_id: str, user_query: str, tenant_user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get comprehensive context for AI Assistant based on tenant database and query.
        
        Args:
            database_connection_id: ID of the database connection
            user_query: The user's natural language query
            tenant_user_id: Optional user ID for personalized context
            
        Returns:
            Dict containing enhanced context for AI
        """
        db = get_db_session()
        try:
            # Get database connection
            db_conn = db.query(DatabaseConnection).filter(
                DatabaseConnection.id == database_connection_id
            ).first()
            
            if not db_conn:
                return {"error": "Database connection not found"}
            
            # Build comprehensive context
            context = {
                "database_info": self._get_database_info(db, db_conn),
                "schema_context": self._get_schema_context(db, db_conn, user_query),
                "business_context": self._get_business_context(db, db_conn),
                "learning_context": self._get_learning_context(db, db_conn, user_query),
                "user_context": self._get_user_context(db, tenant_user_id) if tenant_user_id else {},
                "query_suggestions": self._get_query_suggestions(db, db_conn, user_query),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting enhanced context: {e}")
            return {"error": str(e)}
        finally:
            db.close()
    
    def _get_database_info(self, db: Session, db_conn: DatabaseConnection) -> Dict[str, Any]:
        """Get basic database information."""
        return {
            "name": db_conn.name,
            "type": db_conn.db_type,
            "database_name": db_conn.database_name,
            "status": db_conn.status,
            "last_tested": db_conn.last_tested.isoformat() if db_conn.last_tested else None,
            "schema_last_updated": db_conn.schema_last_updated.isoformat() if db_conn.schema_last_updated else None
        }
    
    def _get_schema_context(self, db: Session, db_conn: DatabaseConnection, user_query: str) -> Dict[str, Any]:
        """Get relevant schema context based on user query."""
        # Get all tables for this database
        tables = db.query(DatabaseTable).filter(
            DatabaseTable.database_connection_id == db_conn.id
        ).all()
        
        # Analyze query to find relevant tables
        relevant_tables = self._find_relevant_tables(tables, user_query)
        
        schema_context = {
            "total_tables": len(tables),
            "relevant_tables": [],
            "table_relationships": [],
            "suggested_joins": []
        }
        
        for table in relevant_tables:
            # Get table details
            table_info = {
                "name": table.table_name,
                "business_name": table.business_name,
                "description": table.business_description,
                "domain": table.business_domain,
                "row_count": table.row_count,
                "ai_description": table.ai_description,
                "columns": []
            }
            
            # Get columns
            columns = db.query(DatabaseColumn).filter(
                DatabaseColumn.table_id == table.id
            ).all()
            
            for col in columns:
                column_info = {
                    "name": col.column_name,
                    "type": col.data_type,
                    "business_name": col.business_name,
                    "description": col.business_description,
                    "category": col.data_category,
                    "is_primary_key": col.is_primary_key,
                    "is_foreign_key": col.is_foreign_key,
                    "sample_values": col.sample_values,
                    "ai_description": col.ai_description
                }
                table_info["columns"].append(column_info)
            
            schema_context["relevant_tables"].append(table_info)
            
            # Get relationships for this table
            relationships = db.query(TableRelationship).filter(
                TableRelationship.source_table_id == table.id
            ).all()
            
            for rel in relationships:
                target_table = db.query(DatabaseTable).filter(
                    DatabaseTable.id == rel.target_table_id
                ).first()
                
                if target_table:
                    rel_info = {
                        "source_table": table.table_name,
                        "target_table": target_table.table_name,
                        "type": rel.relationship_type,
                        "source_columns": rel.source_columns,
                        "target_columns": rel.target_columns,
                        "description": rel.business_description,
                        "ai_description": rel.ai_description
                    }
                    schema_context["table_relationships"].append(rel_info)
        
        # Generate suggested joins
        schema_context["suggested_joins"] = self._generate_suggested_joins(
            schema_context["relevant_tables"], 
            schema_context["table_relationships"]
        )
        
        return schema_context
    
    def _find_relevant_tables(self, tables: List[DatabaseTable], user_query: str) -> List[DatabaseTable]:
        """Find tables relevant to the user's query."""
        query_lower = user_query.lower()
        relevant_tables = []
        
        for table in tables:
            # Check if table name is mentioned
            if table.table_name.lower() in query_lower:
                relevant_tables.append(table)
                continue
            
            # Check if business name is mentioned
            if table.business_name and table.business_name.lower() in query_lower:
                relevant_tables.append(table)
                continue
            
            # Check if domain keywords match
            if table.business_domain:
                domain_keywords = {
                    "user_management": ["user", "customer", "client", "account", "member"],
                    "sales": ["order", "purchase", "transaction", "payment", "sale", "revenue"],
                    "inventory": ["product", "item", "inventory", "stock", "catalog"],
                    "human_resources": ["employee", "staff", "hr", "payroll"],
                    "audit": ["log", "audit", "history", "track"]
                }
                
                keywords = domain_keywords.get(table.business_domain, [])
                if any(keyword in query_lower for keyword in keywords):
                    relevant_tables.append(table)
        
        # If no specific tables found, return tables from common domains
        if not relevant_tables:
            common_domains = ["user_management", "sales", "inventory"]
            relevant_tables = [t for t in tables if t.business_domain in common_domains][:3]
        
        return relevant_tables
    
    def _get_business_context(self, db: Session, db_conn: DatabaseConnection) -> Dict[str, Any]:
        """Get business context for the tenant."""
        # Get tenant AI profile
        ai_profile = db.query(TenantAIProfile).filter(
            TenantAIProfile.database_connection_id == db_conn.id
        ).first()
        
        if ai_profile:
            return {
                "business_domain": ai_profile.business_domain,
                "industry_vertical": ai_profile.industry_vertical,
                "business_description": ai_profile.business_description,
                "preferred_query_style": ai_profile.preferred_query_style,
                "response_tone": ai_profile.response_tone,
                "explanation_level": ai_profile.explanation_level,
                "business_glossary": ai_profile.business_glossary,
                "common_abbreviations": ai_profile.common_abbreviations,
                "preferred_terminology": ai_profile.preferred_terminology
            }
        else:
            # Infer basic context from database connection
            return {
                "business_domain": "general",
                "preferred_query_style": "simple",
                "response_tone": "professional",
                "explanation_level": "intermediate"
            }
    
    def _get_learning_context(self, db: Session, db_conn: DatabaseConnection, user_query: str) -> Dict[str, Any]:
        """Get learning context from past interactions."""
        # Get recent successful queries
        recent_queries = db.query(QueryHistory).filter(
            and_(
                QueryHistory.database_connection_id == db_conn.id,
                QueryHistory.execution_status == 'success',
                QueryHistory.created_at >= datetime.utcnow() - timedelta(days=30)
            )
        ).order_by(desc(QueryHistory.created_at)).limit(10).all()
        
        # Get feedback data
        feedback_data = db.query(AITrainingFeedback).filter(
            and_(
                AITrainingFeedback.database_connection_id == db_conn.id,
                AITrainingFeedback.feedback_type.in_(['positive', 'correction'])
            )
        ).order_by(desc(AITrainingFeedback.created_at)).limit(5).all()
        
        learning_context = {
            "successful_patterns": [],
            "common_queries": [],
            "user_preferences": [],
            "problematic_areas": []
        }
        
        # Analyze successful queries
        for query in recent_queries:
            if query.natural_language_query and query.generated_sql:
                pattern = {
                    "nl_query": query.natural_language_query,
                    "sql_query": query.generated_sql,
                    "tables_involved": query.tables_involved,
                    "complexity": query.query_complexity
                }
                learning_context["successful_patterns"].append(pattern)
        
        # Analyze feedback
        for feedback in feedback_data:
            if feedback.feedback_type == 'positive':
                learning_context["user_preferences"].append({
                    "query": feedback.natural_language_query,
                    "response": feedback.ai_response,
                    "score": feedback.feedback_score
                })
            elif feedback.feedback_type == 'correction':
                learning_context["problematic_areas"].append({
                    "original_query": feedback.natural_language_query,
                    "generated_sql": feedback.generated_sql,
                    "corrected_sql": feedback.corrected_sql,
                    "user_feedback": feedback.user_feedback
                })
        
        return learning_context
    
    def _get_user_context(self, db: Session, tenant_user_id: int) -> Dict[str, Any]:
        """Get user-specific context."""
        user = db.query(TenantUser).filter(TenantUser.id == tenant_user_id).first()
        
        if not user:
            return {}
        
        # Get user's query history
        user_queries = db.query(QueryHistory).filter(
            and_(
                QueryHistory.tenant_user_id == tenant_user_id,
                QueryHistory.created_at >= datetime.utcnow() - timedelta(days=7)
            )
        ).order_by(desc(QueryHistory.created_at)).limit(5).all()
        
        return {
            "user_name": user.name,
            "user_role": user.role,
            "recent_queries": [q.natural_language_query for q in user_queries],
            "query_complexity_preference": self._infer_user_complexity_preference(user_queries)
        }
    
    def _infer_user_complexity_preference(self, queries: List[QueryHistory]) -> str:
        """Infer user's preferred query complexity level."""
        if not queries:
            return "intermediate"
        
        complexity_counts = {"simple": 0, "medium": 0, "complex": 0}
        for query in queries:
            if query.query_complexity:
                complexity_counts[query.query_complexity] = complexity_counts.get(query.query_complexity, 0) + 1
        
        return max(complexity_counts, key=complexity_counts.get) if complexity_counts else "intermediate"
    
    def _get_query_suggestions(self, db: Session, db_conn: DatabaseConnection, user_query: str) -> List[str]:
        """Get query suggestions based on context."""
        suggestions = []
        
        # Get tables for this database
        tables = db.query(DatabaseTable).filter(
            DatabaseTable.database_connection_id == db_conn.id
        ).all()
        
        # Add common query suggestions based on available tables
        for table in tables[:3]:  # Limit to top 3 tables
            if table.common_queries:
                suggestions.extend(table.common_queries[:2])  # Add top 2 queries per table
        
        return suggestions[:5]  # Return top 5 suggestions
    
    def _generate_suggested_joins(self, tables: List[Dict], relationships: List[Dict]) -> List[str]:
        """Generate suggested JOIN patterns."""
        joins = []
        
        if len(tables) >= 2:
            for rel in relationships:
                source_table = rel["source_table"]
                target_table = rel["target_table"]
                source_cols = ", ".join(rel["source_columns"])
                target_cols = ", ".join(rel["target_columns"])
                
                join_suggestion = f"JOIN {target_table} ON {source_table}.{source_cols} = {target_table}.{target_cols}"
                joins.append(join_suggestion)
        
        return joins[:3]  # Return top 3 join suggestions
