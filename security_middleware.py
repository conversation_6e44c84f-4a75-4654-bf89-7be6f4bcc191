"""
Security middleware for the FastAPI application.

This module provides security headers, CORS configuration,
rate limiting, and other security measures for production deployment.
"""

import time
import logging
from typing import Dict, List, Optional
from collections import defaultdict
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os

logger = logging.getLogger(__name__)

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to all responses."""
    
    def __init__(self, app, enable_csp: bool = True):
        super().__init__(app)
        self.enable_csp = enable_csp
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Security headers
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
        
        # Content Security Policy (relaxed for development)
        if self.enable_csp:
            import os
            env = os.getenv("APP_ENV", "development").lower()

            if env == "development":
                # More permissive CSP for development (allows FastAPI docs)
                csp_policy = (
                    "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; "
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                    "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                    "img-src 'self' data: https: blob:; "
                    "font-src 'self' data: https://cdn.jsdelivr.net; "
                    "connect-src 'self' https://api.openai.com; "
                    "frame-ancestors 'none'; "
                    "base-uri 'self'; "
                    "form-action 'self'"
                )
            else:
                # Strict CSP for production
                csp_policy = (
                    "default-src 'self'; "
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                    "style-src 'self' 'unsafe-inline'; "
                    "img-src 'self' data: https:; "
                    "font-src 'self' data:; "
                    "connect-src 'self' https://api.openai.com; "
                    "frame-ancestors 'none'; "
                    "base-uri 'self'; "
                    "form-action 'self'"
                )
            security_headers["Content-Security-Policy"] = csp_policy
        
        # Add headers to response
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware with different limits for different endpoints."""
    
    def __init__(self, app):
        super().__init__(app)
        self.requests = defaultdict(list)
        
        # Rate limit configurations
        self.rate_limits = {
            "/auth/login": {"max_requests": 5, "window_seconds": 300},  # 5 requests per 5 minutes
            "/auth/register": {"max_requests": 3, "window_seconds": 3600},  # 3 requests per hour
            "/auth/refresh": {"max_requests": 10, "window_seconds": 300},  # 10 requests per 5 minutes
            "/database/query": {"max_requests": 100, "window_seconds": 3600},  # 100 requests per hour
            "/ai/classify-intent": {"max_requests": 200, "window_seconds": 3600},  # 200 requests per hour
            "default": {"max_requests": 1000, "window_seconds": 3600}  # Default limit
        }
    
    def get_client_identifier(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Try to get user ID from token if available
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                # This would need to be integrated with JWT validation
                # For now, use IP address
                pass
            except:
                pass
        
        # Fallback to IP address
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        return request.client.host if request.client else "unknown"
    
    def get_rate_limit_config(self, path: str) -> Dict[str, int]:
        """Get rate limit configuration for a specific path."""
        # Check for exact match first
        if path in self.rate_limits:
            return self.rate_limits[path]
        
        # Check for pattern matches
        for pattern, config in self.rate_limits.items():
            if pattern != "default" and path.startswith(pattern):
                return config
        
        return self.rate_limits["default"]
    
    async def dispatch(self, request: Request, call_next):
        client_id = self.get_client_identifier(request)
        path = request.url.path
        current_time = time.time()
        
        # Get rate limit configuration
        config = self.get_rate_limit_config(path)
        max_requests = config["max_requests"]
        window_seconds = config["window_seconds"]
        
        # Create unique key for this client and endpoint
        key = f"{client_id}:{path}"
        
        # Clean old requests outside the window
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if current_time - req_time < window_seconds
        ]
        
        # Check if limit exceeded
        if len(self.requests[key]) >= max_requests:
            logger.warning(f"Rate limit exceeded for {client_id} on {path}")
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "detail": "Rate limit exceeded. Please try again later.",
                    "retry_after": window_seconds
                },
                headers={"Retry-After": str(window_seconds)}
            )
        
        # Add current request
        self.requests[key].append(current_time)
        
        # Add rate limit headers to response
        response = await call_next(request)
        response.headers["X-RateLimit-Limit"] = str(max_requests)
        response.headers["X-RateLimit-Remaining"] = str(max_requests - len(self.requests[key]))
        response.headers["X-RateLimit-Reset"] = str(int(current_time + window_seconds))
        
        return response

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Log all requests for monitoring and debugging."""
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Log request
        logger.info(f"Request: {request.method} {request.url.path}")
        
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Response: {response.status_code} - "
            f"Time: {process_time:.3f}s - "
            f"Path: {request.url.path}"
        )
        
        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response

def configure_cors_middleware(app, allowed_origins: Optional[List[str]] = None):
    """Configure CORS middleware with secure defaults."""
    
    if allowed_origins is None:
        # Default allowed origins based on environment
        env = os.getenv("APP_ENV", "development").lower()
        if env == "production":
            allowed_origins = [
                "https://yourdomain.com",
                "https://www.yourdomain.com"
            ]
        else:
            allowed_origins = [
                "http://localhost:3000",
                "http://localhost:5173",
                "http://localhost:5174"
            ]
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=[
            "Accept",
            "Accept-Language",
            "Content-Language",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "X-CSRF-Token"
        ],
        expose_headers=[
            "X-RateLimit-Limit",
            "X-RateLimit-Remaining",
            "X-RateLimit-Reset",
            "X-Process-Time"
        ]
    )

class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """IP whitelist middleware for admin endpoints."""
    
    def __init__(self, app, whitelist: List[str] = None, protected_paths: List[str] = None):
        super().__init__(app)
        self.whitelist = whitelist or []
        self.protected_paths = protected_paths or ["/admin"]
    
    def get_client_ip(self, request: Request) -> str:
        """Get the real client IP address."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def dispatch(self, request: Request, call_next):
        path = request.url.path
        
        # Check if path is protected
        is_protected = any(path.startswith(protected_path) for protected_path in self.protected_paths)
        
        if is_protected and self.whitelist:
            client_ip = self.get_client_ip(request)
            
            if client_ip not in self.whitelist:
                logger.warning(f"Access denied for IP {client_ip} to protected path {path}")
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={"detail": "Access denied"}
                )
        
        return await call_next(request)

def setup_security_middleware(app):
    """Set up all security middleware for the application."""
    
    # Environment-based configuration
    env = os.getenv("APP_ENV", "development").lower()
    enable_csp = os.getenv("ENABLE_CSP", "true").lower() == "true"
    
    # Add middleware in reverse order (last added = first executed)
    
    # Request logging (should be first to log everything)
    app.add_middleware(RequestLoggingMiddleware)
    
    # Rate limiting
    app.add_middleware(RateLimitMiddleware)
    
    # Security headers
    app.add_middleware(SecurityHeadersMiddleware, enable_csp=enable_csp)
    
    # IP whitelist for admin endpoints (only in production)
    if env == "production":
        admin_whitelist = os.getenv("ADMIN_IP_WHITELIST", "").split(",")
        admin_whitelist = [ip.strip() for ip in admin_whitelist if ip.strip()]

        if admin_whitelist:
            app.add_middleware(
                IPWhitelistMiddleware,
                whitelist=admin_whitelist,
                protected_paths=["/admin"]
            )
    else:
        logger.info("IP whitelist disabled in development mode")
    
    # CORS (should be last to handle preflight requests)
    configure_cors_middleware(app)
    
    logger.info("Security middleware configured successfully")
