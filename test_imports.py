#!/usr/bin/env python3
"""
Test script to verify all imports work correctly.
This helps identify import issues before running the full application.
"""

import sys
import traceback

def test_import(module_name, description=""):
    """Test importing a module and report results."""
    try:
        __import__(module_name)
        print(f"✅ {module_name} {description}")
        return True
    except Exception as e:
        print(f"❌ {module_name} {description}: {e}")
        return False

def test_specific_imports():
    """Test specific problematic imports."""
    success = True
    
    print("🔍 Testing core dependencies...")
    success &= test_import("fastapi", "- FastAPI framework")
    success &= test_import("pydantic", "- Data validation")
    success &= test_import("uvicorn", "- ASGI server")
    success &= test_import("jinja2", "- Template engine")
    
    print("\n🔍 Testing authentication dependencies...")
    success &= test_import("passlib", "- Password hashing library")
    success &= test_import("bcrypt", "- Bcrypt hashing")
    success &= test_import("jwt", "- JWT tokens")
    
    print("\n🔍 Testing database dependencies...")
    success &= test_import("sqlalchemy", "- ORM")
    success &= test_import("pymysql", "- MySQL driver")
    success &= test_import("psycopg2", "- PostgreSQL driver")
    
    print("\n🔍 Testing AI dependencies...")
    success &= test_import("openai", "- OpenAI API")
    success &= test_import("langchain", "- LangChain framework")
    
    print("\n🔍 Testing middleware imports...")
    try:
        from starlette.middleware.base import BaseHTTPMiddleware
        print("✅ BaseHTTPMiddleware - Middleware base class")
    except Exception as e:
        print(f"❌ BaseHTTPMiddleware: {e}")
        success = False
    
    print("\n🔍 Testing application modules...")
    try:
        from api_versioning import APIVersionMiddleware
        print("✅ APIVersionMiddleware - API versioning")
    except Exception as e:
        print(f"❌ APIVersionMiddleware: {e}")
        success = False
        
    try:
        from security_middleware import setup_security_middleware
        print("✅ security_middleware - Security setup")
    except Exception as e:
        print(f"❌ security_middleware: {e}")
        success = False
        
    try:
        from enhanced_security import EnhancedSecurityMiddleware
        print("✅ EnhancedSecurityMiddleware - Enhanced security")
    except Exception as e:
        print(f"❌ EnhancedSecurityMiddleware: {e}")
        success = False
    
    return success

def test_bcrypt_specifically():
    """Test bcrypt functionality specifically."""
    print("\n🔍 Testing bcrypt functionality...")
    try:
        import bcrypt
        print(f"✅ bcrypt version: {bcrypt.__version__}")
        
        # Test basic bcrypt functionality
        password = b"test_password"
        hashed = bcrypt.hashpw(password, bcrypt.gensalt())
        if bcrypt.checkpw(password, hashed):
            print("✅ bcrypt hashing and verification works")
            return True
        else:
            print("❌ bcrypt verification failed")
            return False
            
    except AttributeError as e:
        if "__about__" in str(e):
            print("⚠️  bcrypt __about__ attribute missing (known issue with some versions)")
            print("   This might not affect functionality, testing basic operations...")
            try:
                import bcrypt
                password = b"test_password"
                hashed = bcrypt.hashpw(password, bcrypt.gensalt())
                if bcrypt.checkpw(password, hashed):
                    print("✅ bcrypt basic functionality works despite __about__ issue")
                    return True
            except Exception as e2:
                print(f"❌ bcrypt basic functionality failed: {e2}")
                return False
        else:
            print(f"❌ bcrypt AttributeError: {e}")
            return False
    except Exception as e:
        print(f"❌ bcrypt error: {e}")
        return False

def test_passlib_with_bcrypt():
    """Test passlib with bcrypt backend."""
    print("\n🔍 Testing passlib with bcrypt...")
    try:
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Test password hashing
        password = "test_password_123"
        hashed = pwd_context.hash(password)
        
        if pwd_context.verify(password, hashed):
            print("✅ passlib with bcrypt works correctly")
            return True
        else:
            print("❌ passlib bcrypt verification failed")
            return False
            
    except Exception as e:
        print(f"❌ passlib with bcrypt error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing application imports and dependencies...\n")
    
    overall_success = True
    
    # Test general imports
    overall_success &= test_specific_imports()
    
    # Test bcrypt specifically
    overall_success &= test_bcrypt_specifically()
    
    # Test passlib with bcrypt
    overall_success &= test_passlib_with_bcrypt()
    
    print("\n" + "="*50)
    if overall_success:
        print("🎉 All imports and dependencies are working correctly!")
        sys.exit(0)
    else:
        print("❌ Some imports or dependencies have issues.")
        print("   Please check the errors above and fix them before running the application.")
        sys.exit(1)
