#!/usr/bin/env python3
"""
Production Startup Script

This script handles the complete startup process for production deployment,
including environment validation, database setup, and server initialization.
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Any
import argparse

def setup_logging():
    """Set up logging for the startup script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def print_banner():
    """Print startup banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    SaaS Backend Production                   ║
    ║                      Starting Server...                     ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def validate_environment() -> Dict[str, Any]:
    """Validate production environment variables."""
    logging.info("🔍 Validating environment configuration...")
    
    required_vars = [
        "PROD_DATABASE_URL",
        "JWT_SECRET_KEY", 
        "OPENAI_API_KEY"
    ]
    
    optional_vars = [
        "APP_ENV",
        "WORKERS",
        "HOST",
        "PORT",
        "MYSQL_PASSWORD",
        "TRUSTED_HOSTS"
    ]
    
    validation_results = {
        "status": "success",
        "errors": [],
        "warnings": [],
        "config": {}
    }
    
    # Check required variables
    for var in required_vars:
        value = os.getenv(var)
        if not value or value in ["your_openai_api_key_here", "CHANGE_THIS", "password"]:
            validation_results["errors"].append(f"❌ {var} is missing or has default value")
        else:
            validation_results["config"][var] = "✅ Configured"
    
    # Check optional variables
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            validation_results["config"][var] = f"✅ {value}"
        else:
            validation_results["warnings"].append(f"⚠️  {var} not set (using default)")
    
    # Validate JWT secret strength
    jwt_secret = os.getenv("JWT_SECRET_KEY", "")
    if len(jwt_secret) < 32:
        validation_results["errors"].append("❌ JWT_SECRET_KEY should be at least 32 characters")
    
    # Check if running as root (security warning)
    if os.geteuid() == 0:
        validation_results["warnings"].append("⚠️  Running as root user (not recommended)")
    
    if validation_results["errors"]:
        validation_results["status"] = "error"
    elif validation_results["warnings"]:
        validation_results["status"] = "warning"
    
    return validation_results

def check_database_connectivity() -> bool:
    """Check database connectivity before starting server."""
    logging.info("🗄️  Checking database connectivity...")
    
    try:
        from mysql_manager import get_mysql_manager
        manager = get_mysql_manager()
        
        # Test connection with retries
        max_retries = 5
        for attempt in range(max_retries):
            try:
                result = manager.test_connection()
                if result["status"] == "success":
                    logging.info(f"✅ Database connected: {result['mysql_version']}")
                    return True
                else:
                    logging.warning(f"⚠️  Database connection attempt {attempt + 1} failed: {result.get('error')}")
            except Exception as e:
                logging.warning(f"⚠️  Database connection attempt {attempt + 1} failed: {e}")
            
            if attempt < max_retries - 1:
                logging.info(f"🔄 Retrying in 5 seconds... ({attempt + 1}/{max_retries})")
                time.sleep(5)
        
        logging.error("❌ Failed to connect to database after all retries")
        return False
        
    except Exception as e:
        logging.error(f"❌ Database connectivity check failed: {e}")
        return False

def initialize_database() -> bool:
    """Initialize database tables and setup."""
    logging.info("🔧 Initializing database...")
    
    try:
        from database import create_tables
        from mysql_manager import setup_mysql_database
        
        # Set up MySQL database
        if not setup_mysql_database():
            logging.error("❌ MySQL database setup failed")
            return False
        
        # Create tables
        create_tables()
        logging.info("✅ Database tables initialized")
        return True
        
    except Exception as e:
        logging.error(f"❌ Database initialization failed: {e}")
        return False

def create_directories():
    """Create necessary directories for production."""
    logging.info("📁 Creating necessary directories...")
    
    directories = [
        "logs",
        "backups", 
        "uploads",
        "tmp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logging.info(f"✅ Created directory: {directory}")

def check_dependencies() -> Dict[str, bool]:
    """Check external dependencies."""
    logging.info("🔍 Checking external dependencies...")
    
    dependencies = {}
    
    # Check OpenAI API
    try:
        import openai
        client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        # Simple test - just check if client can be created
        dependencies["openai"] = True
        logging.info("✅ OpenAI client initialized")
    except Exception as e:
        dependencies["openai"] = False
        logging.warning(f"⚠️  OpenAI client initialization failed: {e}")
    
    # Check Redis (if configured)
    redis_url = os.getenv("REDIS_URL")
    if redis_url:
        try:
            import redis
            r = redis.from_url(redis_url)
            r.ping()
            dependencies["redis"] = True
            logging.info("✅ Redis connection successful")
        except Exception as e:
            dependencies["redis"] = False
            logging.warning(f"⚠️  Redis connection failed: {e}")
    
    return dependencies

def start_server(server_type: str = "gunicorn"):
    """Start the production server."""
    logging.info(f"🚀 Starting {server_type} server...")
    
    if server_type == "gunicorn":
        # Start with Gunicorn
        cmd = [
            "gunicorn",
            "main:app",
            "--bind", f"{os.getenv('HOST', '0.0.0.0')}:{os.getenv('PORT', '8000')}",
            "--workers", os.getenv("WORKERS", "4"),
            "--worker-class", "uvicorn.workers.UvicornWorker",
            "--max-requests", os.getenv("MAX_REQUESTS", "1000"),
            "--max-requests-jitter", os.getenv("MAX_REQUESTS_JITTER", "100"),
            "--timeout", os.getenv("TIMEOUT", "30"),
            "--keep-alive", os.getenv("KEEP_ALIVE", "5"),
            "--access-logfile", "-",
            "--error-logfile", "-",
            "--log-level", os.getenv("LOG_LEVEL", "info").lower(),
            "--preload"
        ]
    else:
        # Start with Uvicorn
        cmd = [
            "uvicorn",
            "main:app",
            "--host", os.getenv("HOST", "0.0.0.0"),
            "--port", os.getenv("PORT", "8000"),
            "--workers", os.getenv("WORKERS", "1"),
            "--log-level", os.getenv("LOG_LEVEL", "info").lower()
        ]
    
    logging.info(f"Command: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        logging.info("🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        logging.error(f"❌ Server failed to start: {e}")
        sys.exit(1)

def main():
    """Main startup function."""
    parser = argparse.ArgumentParser(description="Production Server Startup")
    parser.add_argument("--server", choices=["gunicorn", "uvicorn"], default="gunicorn",
                       help="Server type to use")
    parser.add_argument("--skip-db-check", action="store_true",
                       help="Skip database connectivity check")
    parser.add_argument("--skip-init", action="store_true",
                       help="Skip database initialization")
    args = parser.parse_args()
    
    # Set up logging
    setup_logging()
    
    # Print banner
    print_banner()
    
    # Validate environment
    validation = validate_environment()
    
    # Print configuration
    logging.info("📋 Environment Configuration:")
    for key, value in validation["config"].items():
        logging.info(f"   {key}: {value}")
    
    # Print warnings
    for warning in validation["warnings"]:
        logging.warning(warning)
    
    # Check for errors
    if validation["errors"]:
        logging.error("❌ Environment validation failed:")
        for error in validation["errors"]:
            logging.error(f"   {error}")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Check database connectivity
    if not args.skip_db_check:
        if not check_database_connectivity():
            logging.error("❌ Cannot start server without database connectivity")
            sys.exit(1)
    
    # Initialize database
    if not args.skip_init:
        if not initialize_database():
            logging.error("❌ Database initialization failed")
            sys.exit(1)
    
    # Check dependencies
    deps = check_dependencies()
    failed_deps = [name for name, status in deps.items() if not status]
    if failed_deps:
        logging.warning(f"⚠️  Some dependencies failed: {failed_deps}")
        logging.warning("Server will start but some features may not work correctly")
    
    # Start server
    logging.info("🎉 All checks passed! Starting production server...")
    start_server(args.server)

if __name__ == "__main__":
    main()
