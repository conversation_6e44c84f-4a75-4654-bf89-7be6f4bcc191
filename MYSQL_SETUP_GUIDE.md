# MySQL Setup Guide for All Environments

This guide helps you set up MySQL databases for development, test, and production environments.

## 🗄️ **Overview**

All environments now use MySQL consistently:
- **Development**: `saas_dev_db` (uses root user for simplicity)
- **Test**: `saas_test_db` (uses root user for fast testing)
- **Production**: `saas_prod_db` (uses dedicated `saas_user` for security)

## 📋 **Prerequisites**

1. **MySQL Server 8.0+** installed and running
2. **Python dependencies** installed:
   ```bash
   pip install pymysql python-dotenv
   ```

## 🚀 **Quick Setup (All Environments)**

### Option 1: Automated Setup
```bash
# Set up all environments at once
python setup_mysql_all_envs.py

# Or set up specific environment
python setup_mysql_all_envs.py --env dev    # Development only
python setup_mysql_all_envs.py --env test   # Test only  
python setup_mysql_all_envs.py --env prod   # Production only
```

### Option 2: Manual Setup

#### 1. Connect to MySQL as root:
```sql
mysql -u root -p
```

#### 2. Create databases:
```sql
-- Development database
CREATE DATABASE saas_dev_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Test database  
CREATE DATABASE saas_test_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Production database
CREATE DATABASE saas_prod_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 3. Create production user:
```sql
-- Create dedicated user for production
CREATE USER 'saas_user'@'%' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON saas_prod_db.* TO 'saas_user'@'%';
FLUSH PRIVILEGES;
```

## ⚙️ **Environment Configuration**

### Development Environment
```bash
# Copy development template
cp .env.development .env

# Edit .env and update:
DEV_DATABASE_URL=mysql+pymysql://root:YOUR_ROOT_PASSWORD@localhost:3306/saas_dev_db
OPENAI_API_KEY=your_openai_api_key_here
```

### Test Environment
```bash
# For testing, use:
cp .env.test .env

# Or set environment variable:
export APP_ENV=test
```

### Production Environment
```bash
# Copy production template
cp .env.production .env

# Edit .env and update:
PROD_DATABASE_URL=mysql+pymysql://saas_user:YOUR_SECURE_PASSWORD@localhost:3306/saas_prod_db
JWT_SECRET_KEY=GENERATE_SECURE_JWT_SECRET_KEY
OPENAI_API_KEY=your_openai_api_key_here
```

## 🧪 **Testing Your Setup**

### 1. Test Database Connections
```bash
# Test all MySQL functionality
python test_mysql.py

# Test authentication system
python test_auth.py
```

### 2. Test Specific Environment
```bash
# Test development
APP_ENV=development python test_mysql.py

# Test production setup
APP_ENV=production python test_mysql.py
```

## 🐳 **Docker Setup**

### Development with Docker
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Check logs
docker-compose -f docker-compose.dev.yml logs -f
```

### Production with Docker
```bash
# Create .env file with production settings
cp .env.production .env

# Update docker environment variables
export MYSQL_ROOT_PASSWORD=secure_root_password
export MYSQL_PASSWORD=secure_user_password
export JWT_SECRET_KEY=$(python -c "import secrets; print(secrets.token_urlsafe(32))")

# Start production environment
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 **Database Management**

### Backup Database
```bash
# Create backup
mysqldump -u root -p saas_dev_db > backup_dev.sql
mysqldump -u saas_user -p saas_prod_db > backup_prod.sql

# Or use the API endpoint (requires admin auth)
curl -X POST "http://localhost:8000/database/mysql/backup" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Restore Database
```bash
# Restore from backup
mysql -u root -p saas_dev_db < backup_dev.sql
```

### Optimize Database
```bash
# Use the API endpoint (requires admin auth)
curl -X POST "http://localhost:8000/database/mysql/optimize" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔍 **Troubleshooting**

### Common Issues

#### 1. Connection Refused
```bash
# Check if MySQL is running
sudo systemctl status mysql

# Start MySQL if not running
sudo systemctl start mysql
```

#### 2. Access Denied
```bash
# Reset root password if needed
sudo mysql_secure_installation

# Or connect without password and set one
sudo mysql
ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
```

#### 3. Database Not Found
```bash
# Run the setup script again
python setup_mysql_all_envs.py --env dev
```

#### 4. Character Set Issues
```sql
-- Check database charset
SHOW CREATE DATABASE saas_dev_db;

-- Fix charset if needed
ALTER DATABASE saas_dev_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📊 **Health Checks**

### Check Database Status
```bash
# Public health check (no auth required)
curl http://localhost:8000/database/mysql/health

# Detailed status (requires admin auth)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8000/database/mysql/status
```

### Monitor Database Performance
```sql
-- Check active connections
SHOW STATUS LIKE 'Threads_connected';

-- Check database size
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema IN ('saas_dev_db', 'saas_test_db', 'saas_prod_db')
GROUP BY table_schema;
```

## 🔐 **Security Best Practices**

1. **Use dedicated users** for production (not root)
2. **Strong passwords** for all database users
3. **SSL/TLS connections** for production
4. **Regular backups** with retention policy
5. **Monitor access logs** for suspicious activity
6. **Limit network access** to database server

## 📝 **Next Steps**

After setting up MySQL:

1. ✅ **Test all environments** with the provided scripts
2. ✅ **Configure your application** with the correct environment
3. ✅ **Set up monitoring** and alerting
4. ✅ **Implement backup strategy** for production
5. ✅ **Configure SSL/TLS** for production connections

## 🆘 **Getting Help**

If you encounter issues:

1. Check the application logs
2. Verify MySQL server status
3. Test database connections manually
4. Review environment variable configuration
5. Run the diagnostic scripts provided
