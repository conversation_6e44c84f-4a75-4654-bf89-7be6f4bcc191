#!/bin/bash

echo "🔄 Resetting Docker environment for backend..."

# Stop and remove all containers
echo "Stopping containers..."
docker-compose -f docker-compose.dev.yml down

# Remove volumes to start fresh
echo "Removing volumes..."
docker-compose -f docker-compose.dev.yml down -v

# Remove any orphaned containers
echo "Removing orphaned containers..."
docker container prune -f

# Remove unused volumes
echo "Removing unused volumes..."
docker volume prune -f

# Rebuild and start services
echo "Building and starting services..."
docker-compose -f docker-compose.dev.yml up --build

echo "✅ Docker environment reset complete!"
