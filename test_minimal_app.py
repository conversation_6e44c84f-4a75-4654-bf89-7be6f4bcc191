#!/usr/bin/env python3
"""
Minimal test to verify all dependencies work before running the full app.
"""

def test_imports():
    """Test all critical imports."""
    print("🧪 Testing critical imports...")
    
    imports_to_test = [
        # Core FastAPI
        ("fastapi", "FastAPI"),
        ("uvicorn", None),
        ("pydantic", "BaseModel"),
        ("starlette", None),
        
        # Database
        ("sqlalchemy", None),
        ("pymysql", None),
        
        # Authentication
        ("jwt", None),
        ("passlib.context", "CryptContext"),
        ("bcrypt", None),
        ("jose", "jwt"),
        
        # Security
        ("bleach", None),
        ("user_agents", None),

        # Lang<PERSON>hain
        ("langchain", None),
        ("langchain_community", None),
        ("langchain_openai", None),
        ("openai", None),
        
        # Templates
        ("jinja2", None),
        
        # Environment
        ("dotenv", "load_dotenv"),
        ("cryptography", None),
    ]
    
    failed_imports = []
    
    for module, attr in imports_to_test:
        try:
            if attr:
                exec(f"from {module} import {attr}")
                print(f"✅ {module}.{attr}")
            else:
                exec(f"import {module}")
                print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
        except Exception as e:
            print(f"⚠️  {module}: {e}")
    
    return len(failed_imports) == 0, failed_imports

def test_jwt_functionality():
    """Test JWT functionality specifically."""
    print("\n🔐 Testing JWT functionality...")
    
    try:
        import jwt
        
        # Test encoding
        payload = {"user_id": "123", "email": "<EMAIL>"}
        secret = "test-secret"
        token = jwt.encode(payload, secret, algorithm="HS256")
        print("✅ JWT encode works")
        
        # Test decoding
        decoded = jwt.decode(token, secret, algorithms=["HS256"])
        print("✅ JWT decode works")
        
        if decoded == payload:
            print("✅ JWT round-trip successful")
            return True
        else:
            print("❌ JWT round-trip failed")
            return False
            
    except Exception as e:
        print(f"❌ JWT test failed: {e}")
        return False

def test_minimal_fastapi():
    """Test minimal FastAPI app creation."""
    print("\n🚀 Testing minimal FastAPI app...")
    
    try:
        from fastapi import FastAPI
        from pydantic import BaseModel
        
        app = FastAPI(title="Test App")
        
        class TestModel(BaseModel):
            message: str
        
        @app.get("/")
        def read_root():
            return {"message": "Hello World"}
        
        @app.post("/test")
        def test_endpoint(data: TestModel):
            return {"received": data.message}
        
        print("✅ FastAPI app creation successful")
        return True
        
    except Exception as e:
        print(f"❌ FastAPI test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Minimal Dependency Test")
    print("=" * 50)
    
    # Test imports
    imports_ok, failed = test_imports()
    
    # Test JWT
    jwt_ok = test_jwt_functionality()
    
    # Test FastAPI
    fastapi_ok = test_minimal_fastapi()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"   JWT:     {'✅ PASS' if jwt_ok else '❌ FAIL'}")
    print(f"   FastAPI: {'✅ PASS' if fastapi_ok else '❌ FAIL'}")
    
    if imports_ok and jwt_ok and fastapi_ok:
        print("\n🎉 All tests passed! Your app should work now.")
        print("\nNext steps:")
        print("1. Run: python3 main.py")
        print("2. Or:  uvicorn main:app --reload")
        print("3. Or:  docker-compose -f docker-compose.simple.yml up --build")
        return True
    else:
        print("\n❌ Some tests failed.")
        if failed:
            print(f"Failed imports: {', '.join(failed)}")
        print("\nTry running the dependency fix script:")
        print("bash fix_all_dependencies.sh")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
