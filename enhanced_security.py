"""
Enhanced Security Module

This module provides advanced security features including:
- Advanced threat detection
- Bot detection and blocking
- Geolocation-based filtering
- Adaptive rate limiting
- Security event correlation
- Automated threat response
"""

import time
import logging
import hashlib
import json
import asyncio
from typing import Dict, List, Optional, Set, Any, Tuple
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from collections import defaultdict, deque
from datetime import datetime, timedelta
import ipaddress
import re
from urllib.parse import urlparse
import user_agents

logger = logging.getLogger(__name__)

class ThreatDetector:
    """Advanced threat detection system."""
    
    def __init__(self):
        self.suspicious_patterns = {
            'sql_injection': [
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
                r"(\b(UNION|OR|AND)\s+\d+\s*=\s*\d+)",
                r"(--|#|/\*|\*/)",
                r"(\b(WAITFOR|DELAY|SLEEP)\b)",
                r"(\b(XP_|SP_)\w+)",
                r"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)"
            ],
            'xss': [
                r"<script[^>]*>.*?</script>",
                r"javascript:",
                r"vbscript:",
                r"onload\s*=",
                r"onerror\s*=",
                r"onclick\s*=",
                r"onmouseover\s*=",
                r"<iframe[^>]*>",
                r"<object[^>]*>",
                r"<embed[^>]*>"
            ],
            'command_injection': [
                r"[;&|`$(){}[\]\\]",
                r"\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl)\b",
                r"(\.\.\/|\.\.\\)",
                r"(\$\{|\$\()",
                r"(\|\s*(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl))"
            ],
            'path_traversal': [
                r"(\.\.\/|\.\.\\)",
                r"(%2e%2e%2f|%2e%2e%5c)",
                r"(\.\.%2f|\.\.%5c)",
                r"(%252e%252e%252f|%252e%252e%255c)"
            ]
        }
        
        self.threat_scores = defaultdict(float)
        self.threat_history = defaultdict(list)
    
    def analyze_request(self, request: Request) -> Dict[str, Any]:
        """Analyze request for threats."""
        threat_analysis = {
            'threats_detected': [],
            'threat_score': 0.0,
            'risk_level': 'low',
            'details': {}
        }
        
        # Analyze URL
        url_threats = self._analyze_url(str(request.url))
        threat_analysis['threats_detected'].extend(url_threats)
        
        # Analyze headers
        header_threats = self._analyze_headers(request.headers)
        threat_analysis['threats_detected'].extend(header_threats)
        
        # Analyze user agent
        ua_threats = self._analyze_user_agent(request.headers.get('user-agent', ''))
        threat_analysis['threats_detected'].extend(ua_threats)
        
        # Calculate threat score
        threat_analysis['threat_score'] = len(threat_analysis['threats_detected']) * 10
        
        # Determine risk level
        if threat_analysis['threat_score'] >= 50:
            threat_analysis['risk_level'] = 'critical'
        elif threat_analysis['threat_score'] >= 30:
            threat_analysis['risk_level'] = 'high'
        elif threat_analysis['threat_score'] >= 10:
            threat_analysis['risk_level'] = 'medium'
        
        return threat_analysis
    
    def _analyze_url(self, url: str) -> List[str]:
        """Analyze URL for threats."""
        threats = []
        
        for threat_type, patterns in self.suspicious_patterns.items():
            for pattern in patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    threats.append(f"{threat_type}_in_url")
                    logger.warning(f"Threat detected in URL: {threat_type} - {pattern}")
        
        return threats
    
    def _analyze_headers(self, headers: Dict[str, str]) -> List[str]:
        """Analyze headers for threats."""
        threats = []
        
        # Check for suspicious headers
        suspicious_headers = ['x-forwarded-for', 'x-real-ip', 'x-originating-ip']
        for header in suspicious_headers:
            if header in headers:
                value = headers[header]
                if self._is_suspicious_ip_header(value):
                    threats.append(f"suspicious_{header}")
        
        # Check for header injection
        for name, value in headers.items():
            if '\n' in value or '\r' in value:
                threats.append("header_injection")
                logger.warning(f"Header injection detected: {name}")
        
        return threats
    
    def _analyze_user_agent(self, user_agent: str) -> List[str]:
        """Analyze user agent for threats."""
        threats = []
        
        if not user_agent:
            threats.append("missing_user_agent")
            return threats
        
        # Parse user agent
        try:
            ua = user_agents.parse(user_agent)
            
            # Check for bot patterns
            bot_patterns = [
                r'bot', r'crawler', r'spider', r'scraper', r'scanner',
                r'sqlmap', r'nikto', r'nmap', r'masscan', r'zap',
                r'burp', r'w3af', r'acunetix', r'nessus'
            ]
            
            for pattern in bot_patterns:
                if re.search(pattern, user_agent, re.IGNORECASE):
                    threats.append(f"suspicious_bot_{pattern}")
                    logger.warning(f"Suspicious bot detected: {pattern}")
            
            # Check for unusual user agents
            if len(user_agent) > 500:
                threats.append("oversized_user_agent")
            
            if len(user_agent) < 10:
                threats.append("undersized_user_agent")
        
        except Exception as e:
            logger.warning(f"Failed to parse user agent: {e}")
            threats.append("unparseable_user_agent")
        
        return threats
    
    def _is_suspicious_ip_header(self, value: str) -> bool:
        """Check if IP header value is suspicious."""
        # Check for multiple IPs (potential proxy chain)
        ips = value.split(',')
        if len(ips) > 3:
            return True
        
        # Check for private IP ranges in forwarded headers
        for ip in ips:
            ip = ip.strip()
            try:
                ip_obj = ipaddress.ip_address(ip)
                if ip_obj.is_private and not ip_obj.is_loopback:
                    return True
            except ValueError:
                return True  # Invalid IP format
        
        return False

class AdaptiveRateLimiter:
    """Adaptive rate limiting with threat-based adjustments."""
    
    def __init__(self):
        self.request_counts = defaultdict(lambda: deque())
        self.threat_multipliers = defaultdict(float)
        self.blocked_ips = defaultdict(datetime)
        
        # Base limits
        self.base_limits = {
            'requests_per_minute': 60,
            'requests_per_hour': 1000,
            'burst_limit': 10
        }
    
    def is_allowed(self, client_ip: str, threat_score: float = 0.0) -> Tuple[bool, Dict[str, Any]]:
        """Check if request is allowed based on adaptive limits."""
        now = datetime.now()
        
        # Check if IP is temporarily blocked
        if client_ip in self.blocked_ips:
            if now < self.blocked_ips[client_ip]:
                return False, {
                    'reason': 'temporarily_blocked',
                    'blocked_until': self.blocked_ips[client_ip].isoformat()
                }
            else:
                del self.blocked_ips[client_ip]
        
        # Clean old requests
        self._clean_old_requests(client_ip, now)
        
        # Calculate adaptive limits based on threat score
        adaptive_limits = self._calculate_adaptive_limits(threat_score)
        
        # Check current request count
        current_requests = len(self.request_counts[client_ip])
        
        # Check minute limit
        minute_requests = sum(1 for req_time in self.request_counts[client_ip] 
                             if (now - req_time).total_seconds() <= 60)
        
        if minute_requests >= adaptive_limits['requests_per_minute']:
            # Block for escalating time based on violations
            block_duration = min(300, 60 * (2 ** self.threat_multipliers[client_ip]))
            self.blocked_ips[client_ip] = now + timedelta(seconds=block_duration)
            self.threat_multipliers[client_ip] += 1
            
            return False, {
                'reason': 'rate_limit_exceeded',
                'limit_type': 'per_minute',
                'blocked_for_seconds': block_duration
            }
        
        # Check burst limit
        recent_requests = sum(1 for req_time in self.request_counts[client_ip] 
                             if (now - req_time).total_seconds() <= 10)
        
        if recent_requests >= adaptive_limits['burst_limit']:
            return False, {
                'reason': 'burst_limit_exceeded',
                'limit_type': 'burst'
            }
        
        # Record request
        self.request_counts[client_ip].append(now)
        
        return True, {'status': 'allowed'}
    
    def _clean_old_requests(self, client_ip: str, now: datetime):
        """Clean old request records."""
        cutoff = now - timedelta(hours=1)
        while (self.request_counts[client_ip] and 
               self.request_counts[client_ip][0] < cutoff):
            self.request_counts[client_ip].popleft()
    
    def _calculate_adaptive_limits(self, threat_score: float) -> Dict[str, int]:
        """Calculate adaptive limits based on threat score."""
        # Reduce limits based on threat score
        threat_factor = max(0.1, 1.0 - (threat_score / 100.0))
        
        return {
            'requests_per_minute': int(self.base_limits['requests_per_minute'] * threat_factor),
            'requests_per_hour': int(self.base_limits['requests_per_hour'] * threat_factor),
            'burst_limit': int(self.base_limits['burst_limit'] * threat_factor)
        }

class SecurityEventLogger:
    """Log and correlate security events."""
    
    def __init__(self):
        self.events = deque(maxlen=10000)  # Keep last 10k events
        self.event_counts = defaultdict(int)
    
    def log_event(self, event_type: str, client_ip: str, details: Dict[str, Any]):
        """Log a security event."""
        event = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'client_ip': client_ip,
            'details': details
        }
        
        self.events.append(event)
        self.event_counts[f"{event_type}_{client_ip}"] += 1
        
        # Log to application logger
        logger.warning(f"Security Event: {event_type}", extra=event)
        
        # Check for patterns that might indicate coordinated attack
        self._check_attack_patterns(client_ip, event_type)
    
    def _check_attack_patterns(self, client_ip: str, event_type: str):
        """Check for attack patterns."""
        # Check for repeated events from same IP
        recent_events = [
            e for e in self.events 
            if e['client_ip'] == client_ip and 
            (datetime.now() - datetime.fromisoformat(e['timestamp'])).total_seconds() <= 300
        ]
        
        if len(recent_events) >= 10:
            logger.critical(f"Potential coordinated attack from {client_ip}: {len(recent_events)} events in 5 minutes")
    
    def get_recent_events(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get recent security events."""
        cutoff = datetime.now() - timedelta(minutes=minutes)
        return [
            e for e in self.events 
            if datetime.fromisoformat(e['timestamp']) >= cutoff
        ]

class EnhancedSecurityMiddleware(BaseHTTPMiddleware):
    """Enhanced security middleware with advanced threat detection."""
    
    def __init__(self, app):
        super().__init__(app)
        self.threat_detector = ThreatDetector()
        self.rate_limiter = AdaptiveRateLimiter()
        self.event_logger = SecurityEventLogger()
    
    async def dispatch(self, request: Request, call_next):
        """Process request with enhanced security checks."""
        start_time = time.time()
        client_ip = self._get_client_ip(request)
        
        try:
            # Threat analysis
            threat_analysis = self.threat_detector.analyze_request(request)
            
            # Rate limiting with threat consideration
            is_allowed, rate_limit_info = self.rate_limiter.is_allowed(
                client_ip, threat_analysis['threat_score']
            )
            
            if not is_allowed:
                self.event_logger.log_event(
                    'rate_limit_exceeded',
                    client_ip,
                    rate_limit_info
                )
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "Rate limit exceeded",
                        "details": rate_limit_info
                    }
                )
            
            # Block high-risk requests
            if threat_analysis['risk_level'] in ['critical', 'high']:
                self.event_logger.log_event(
                    'threat_blocked',
                    client_ip,
                    threat_analysis
                )
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "error": "Request blocked due to security policy",
                        "risk_level": threat_analysis['risk_level']
                    }
                )
            
            # Log medium risk requests
            if threat_analysis['risk_level'] == 'medium':
                self.event_logger.log_event(
                    'threat_detected',
                    client_ip,
                    threat_analysis
                )
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            self._add_security_headers(response, threat_analysis)
            
            # Log request completion
            processing_time = time.time() - start_time
            if processing_time > 5.0:  # Log slow requests
                self.event_logger.log_event(
                    'slow_request',
                    client_ip,
                    {'processing_time': processing_time, 'path': request.url.path}
                )
            
            return response
        
        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            self.event_logger.log_event(
                'middleware_error',
                client_ip,
                {'error': str(e)}
            )
            # Continue processing even if security checks fail
            return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        # Check forwarded headers
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else 'unknown'
    
    def _add_security_headers(self, response: Response, threat_analysis: Dict[str, Any]):
        """Add security headers to response."""
        # Add threat information for monitoring
        if threat_analysis['threat_score'] > 0:
            response.headers['X-Threat-Score'] = str(threat_analysis['threat_score'])
            response.headers['X-Risk-Level'] = threat_analysis['risk_level']
