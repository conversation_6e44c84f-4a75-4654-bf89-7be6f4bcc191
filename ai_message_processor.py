"""
AI-Powered Message Processing System
Replaces all hardcoded message detection and response generation with intelligent AI.
"""

import os
import openai
from typing import Dict, Any, Optional
import json
import logging
from datetime import datetime

# Import enhanced context services
from ai_context_service import AIContextService
from schema_discovery_service import SchemaDiscoveryService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIMessageProcessor:
    """
    Intelligent message processor that uses AI to understand user intent
    and generate appropriate responses without hardcoded patterns.
    """
    
    def __init__(self):
        """Initialize the AI message processor."""
        self.openai_client = openai.OpenAI(
            api_key=os.getenv("OPENAI_API_KEY")
        )

        # Initialize enhanced context services
        self.context_service = AIContextService()
        self.schema_service = SchemaDiscoveryService()

        # System context for the AI assistant
        self.system_context = {
            "role": "You are an intelligent database assistant AI",
            "capabilities": [
                "Convert natural language to SQL queries",
                "Help users understand their data",
                "Provide conversational responses",
                "Explain database concepts",
                "Assist with data analysis"
            ],
            "personality": "Helpful, friendly, professional, and knowledgeable",
            "database_types": ["MySQL", "PostgreSQL", "SQLite", "SQL Server"],
            "current_time": datetime.now().isoformat()
        }
    
    def classify_message_intent(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Use AI to classify message intent instead of hardcoded patterns.
        
        Args:
            message: User's message
            context: Additional context (chat history, user info, etc.)
            
        Returns:
            Dict with intent classification and confidence
        """
        try:
            # Prepare context for AI
            context_info = context or {}
            
            # Create prompt for intent classification
            classification_prompt = f"""
            Analyze this user message and classify its intent. Consider the context provided.
            
            User Message: "{message}"
            Context: {json.dumps(context_info, indent=2)}
            
            Classify the intent into one of these categories:
            1. GREETING - Greetings, hellos, how are you, etc.
            2. DATABASE_QUERY - Requests for data, SQL queries, data analysis
            3. HELP_REQUEST - Asking for help, capabilities, instructions
            4. IDENTITY_QUESTION - Who are you, what are you, etc.
            5. THANK_YOU - Expressions of gratitude
            6. GENERAL_CONVERSATION - Other conversational messages
            7. SYSTEM_QUESTION - Questions about the system, features, etc.
            
            Respond with a JSON object containing:
            {{
                "intent": "CATEGORY_NAME",
                "confidence": 0.95,
                "reasoning": "Brief explanation of why this intent was chosen",
                "is_database_related": true/false,
                "requires_sql": true/false,
                "suggested_response_type": "conversational/informational/sql_generation"
            }}
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are an expert at understanding user intent. Always respond with valid JSON."},
                    {"role": "user", "content": classification_prompt}
                ],
                temperature=0.1,
                max_tokens=300
            )
            
            # Parse AI response
            ai_response = response.choices[0].message.content.strip()
            
            # Clean up response if it has markdown formatting
            if ai_response.startswith("```json"):
                ai_response = ai_response.replace("```json", "").replace("```", "").strip()
            
            intent_data = json.loads(ai_response)
            
            return {
                "success": True,
                "intent": intent_data.get("intent", "GENERAL_CONVERSATION"),
                "confidence": intent_data.get("confidence", 0.5),
                "reasoning": intent_data.get("reasoning", ""),
                "is_database_related": intent_data.get("is_database_related", False),
                "requires_sql": intent_data.get("requires_sql", False),
                "suggested_response_type": intent_data.get("suggested_response_type", "conversational"),
                "raw_ai_response": ai_response
            }
            
        except Exception as e:
            logger.error(f"Error in AI intent classification: {e}")
            # Fallback to simple heuristics if AI fails
            return self._fallback_intent_classification(message)
    
    def generate_dynamic_response(self, message: str, intent_data: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """
        Generate dynamic responses using AI instead of hardcoded templates.
        
        Args:
            message: Original user message
            intent_data: Intent classification results
            context: Additional context
            
        Returns:
            AI-generated response string
        """
        try:
            # Prepare context for response generation
            context_info = context or {}
            
            # Create dynamic prompt based on intent
            response_prompt = f"""
            Generate an appropriate response for this user message based on the intent analysis.
            
            User Message: "{message}"
            Intent: {intent_data.get('intent', 'GENERAL_CONVERSATION')}
            Confidence: {intent_data.get('confidence', 0.5)}
            Reasoning: {intent_data.get('reasoning', '')}
            
            System Context:
            - You are a database assistant AI
            - You help users with SQL queries and data analysis
            - You can convert natural language to SQL
            - You are friendly, helpful, and professional
            - Current capabilities: {', '.join(self.system_context['capabilities'])}
            
            Additional Context: {json.dumps(context_info, indent=2)}
            
            Guidelines for response:
            1. Be conversational and natural
            2. Match the user's tone and energy level
            3. If it's a greeting, respond warmly and offer help
            4. If it's a database query, acknowledge and prepare for SQL generation
            5. If it's a help request, provide useful information about capabilities
            6. If it's an identity question, explain your role as a database assistant
            7. Keep responses concise but helpful
            8. Use appropriate emojis sparingly for friendliness
            
            Generate a response that feels natural and helpful:
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a helpful database assistant AI. Generate natural, conversational responses."},
                    {"role": "user", "content": response_prompt}
                ],
                temperature=0.7,
                max_tokens=500
            )
            
            ai_response = response.choices[0].message.content.strip()
            return ai_response
            
        except Exception as e:
            logger.error(f"Error in AI response generation: {e}")
            return self._fallback_response(intent_data.get('intent', 'GENERAL_CONVERSATION'))
    
    def _fallback_intent_classification(self, message: str) -> Dict[str, Any]:
        """Fallback intent classification if AI fails."""
        message_lower = message.lower().strip()
        
        # Simple keyword-based fallback
        if any(word in message_lower for word in ['hi', 'hello', 'hey', 'good morning']):
            return {"success": False, "intent": "GREETING", "confidence": 0.7, "is_database_related": False}
        elif any(word in message_lower for word in ['show', 'list', 'get', 'count', 'select']):
            return {"success": False, "intent": "DATABASE_QUERY", "confidence": 0.6, "is_database_related": True}
        elif any(word in message_lower for word in ['help', 'how', 'what can you']):
            return {"success": False, "intent": "HELP_REQUEST", "confidence": 0.6, "is_database_related": False}
        else:
            return {"success": False, "intent": "GENERAL_CONVERSATION", "confidence": 0.5, "is_database_related": False}
    
    def _fallback_response(self, intent: str) -> str:
        """Fallback responses if AI generation fails."""
        import random

        fallback_responses = {
            "GREETING": [
                "Hello! I'm your database assistant. How can I help you today?",
                "Hi there! Ready to explore your data together?",
                "Good to see you! What database questions do you have?",
                "Hello! I'm here to help you understand and query your data.",
                "Hi! Let's dive into your database. What would you like to know?"
            ],
            "DATABASE_QUERY": [
                "I'd be happy to help you with your database query. Let me process that for you.",
                "Great question! Let me help you find that information in your database.",
                "I'll help you get that data. Let me work on your query.",
                "Perfect! I can help you retrieve that information from your database."
            ],
            "HELP_REQUEST": [
                "I'm here to help! I can convert your questions into SQL queries and help you understand your data.",
                "I'd love to assist you! I specialize in database queries and data analysis.",
                "Happy to help! I can turn your questions into database queries and explain your data.",
                "I'm your database companion! Ask me anything about your data and I'll help you find answers."
            ],
            "IDENTITY_QUESTION": [
                "I'm an AI database assistant designed to help you query and understand your data.",
                "I'm your friendly database AI! I help turn questions into SQL queries and make data easy to understand.",
                "I'm a database assistant AI that specializes in helping you explore and understand your data."
            ],
            "THANK_YOU": [
                "You're welcome! Feel free to ask me anything else.",
                "Happy to help! What else would you like to know about your data?",
                "My pleasure! I'm here whenever you need database assistance.",
                "Glad I could help! Any other data questions?"
            ],
            "GENERAL_CONVERSATION": [
                "I'm here to help with your database needs. What would you like to know?",
                "I specialize in database queries and data analysis. How can I assist you?",
                "I'm your data assistant! What information are you looking for?",
                "Ready to help with your database questions. What's on your mind?"
            ]
        }

        responses = fallback_responses.get(intent, ["I'm here to help! What can I do for you?"])
        return random.choice(responses)

    def process_message_with_database_context(self, message: str, database_connection_id: str,
                                            tenant_user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Process message with enhanced database context for better AI responses.

        Args:
            message: User's message
            database_connection_id: ID of the database connection
            tenant_user_id: Optional user ID for personalized context

        Returns:
            Dict with enhanced AI response and context
        """
        try:
            # Get enhanced context
            enhanced_context = self.context_service.get_enhanced_context(
                database_connection_id=database_connection_id,
                user_query=message,
                tenant_user_id=tenant_user_id
            )

            if "error" in enhanced_context:
                logger.error(f"Failed to get enhanced context: {enhanced_context['error']}")
                # Fallback to basic processing
                return self.classify_message_intent(message)

            # Classify intent with enhanced context
            intent_result = self.classify_message_intent_enhanced(message, enhanced_context)

            # Generate response with enhanced context
            if intent_result.get("requires_sql", False):
                # For SQL queries, provide rich database context
                ai_response = self.generate_sql_response_with_context(message, enhanced_context)
            else:
                # For conversational queries, use enhanced context
                ai_response = self.generate_conversational_response_with_context(message, intent_result, enhanced_context)

            return {
                "sql_query": ai_response.get("sql_query"),
                "content": ai_response.get("content", ai_response.get("response", "")),
                "message_type": intent_result.get("intent", "general").lower(),
                "confidence": intent_result.get("confidence", 0.5),
                "ai_reasoning": intent_result.get("reasoning", ""),
                "enhanced_context_used": True,
                "context_summary": {
                    "relevant_tables": len(enhanced_context.get("schema_context", {}).get("relevant_tables", [])),
                    "business_domain": enhanced_context.get("business_context", {}).get("business_domain"),
                    "user_preferences": enhanced_context.get("user_context", {}).get("query_complexity_preference")
                },
                "success": True
            }

        except Exception as e:
            logger.error(f"Error in enhanced message processing: {e}")
            # Fallback to basic processing
            return self.classify_message_intent(message)


# Import and apply enhanced methods
from ai_enhanced_methods import enhance_ai_processor
enhance_ai_processor()

# Global instance
ai_processor = AIMessageProcessor()
