#!/bin/bash

# Backend Security Implementation - Dependency Installation Script
# This script installs the required dependencies for the new security features

echo "🔐 Installing Backend Security Dependencies..."

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
fi

# Install new dependencies
echo "📥 Installing authentication and security packages..."

pip install python-jose[cryptography]
pip install python-jwt
pip install bcrypt
pip install passlib[bcrypt]

# Verify installation
echo "✅ Verifying installations..."

python -c "import jose; print('✓ python-jose installed successfully')" 2>/dev/null || echo "❌ python-jose installation failed"
python -c "import jwt; print('✓ python-jwt installed successfully')" 2>/dev/null || echo "❌ python-jwt installation failed"
python -c "import bcrypt; print('✓ bcrypt installed successfully')" 2>/dev/null || echo "❌ bcrypt installation failed"
python -c "import passlib; print('✓ passlib installed successfully')" 2>/dev/null || echo "❌ passlib installation failed"

# Update requirements.txt
echo "📝 Updating requirements.txt..."
pip freeze > requirements_new.txt

echo ""
echo "🎉 Installation complete!"
echo ""
echo "Next steps:"
echo "1. Copy .env.example to .env and update with your values"
echo "2. Generate a secure JWT secret key"
echo "3. Test the new authentication endpoints"
echo ""
echo "To generate a secure JWT secret key, run:"
echo "python -c \"import secrets; print(secrets.token_urlsafe(32))\""
echo ""
