#!/usr/bin/env python3
"""
Test script for the new JWT authentication system.
Run this script to verify that the authentication endpoints are working correctly.
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "full_name": "Test User"
}

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*50}")
    print(f"🧪 {title}")
    print('='*50)

def print_result(test_name, success, details=None):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"   Details: {details}")

def test_health_check():
    """Test the health check endpoint."""
    print_section("Health Check")
    try:
        response = requests.get(f"{BASE_URL}/")
        success = response.status_code == 200
        print_result("Health Check", success, f"Status: {response.status_code}")
        if success:
            print(f"   Response: {response.json()}")
        return success
    except Exception as e:
        print_result("Health Check", False, str(e))
        return False

def test_user_registration():
    """Test user registration."""
    print_section("User Registration")
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register",
            json=TEST_USER,
            headers={"Content-Type": "application/json"}
        )
        
        success = response.status_code == 200
        print_result("User Registration", success, f"Status: {response.status_code}")
        
        if success:
            data = response.json()
            print(f"   Access Token: {data['access_token'][:50]}...")
            print(f"   Token Type: {data['token_type']}")
            print(f"   Expires In: {data['expires_in']} seconds")
            return data
        else:
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print_result("User Registration", False, str(e))
        return None

def test_user_login():
    """Test user login."""
    print_section("User Login")
    try:
        login_data = {
            "email": TEST_USER["email"],
            "password": TEST_USER["password"]
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        success = response.status_code == 200
        print_result("User Login", success, f"Status: {response.status_code}")
        
        if success:
            data = response.json()
            print(f"   Access Token: {data['access_token'][:50]}...")
            print(f"   Refresh Token: {data['refresh_token'][:50]}...")
            return data
        else:
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print_result("User Login", False, str(e))
        return None

def test_protected_endpoint(access_token):
    """Test accessing a protected endpoint."""
    print_section("Protected Endpoint Access")
    try:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        
        success = response.status_code == 200
        print_result("Get Current User", success, f"Status: {response.status_code}")
        
        if success:
            data = response.json()
            print(f"   User ID: {data['id']}")
            print(f"   Email: {data['email']}")
            print(f"   Full Name: {data['full_name']}")
            print(f"   Is Active: {data['is_active']}")
            return True
        else:
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print_result("Get Current User", False, str(e))
        return False

def test_token_refresh(refresh_token):
    """Test token refresh."""
    print_section("Token Refresh")
    try:
        response = requests.post(
            f"{BASE_URL}/auth/refresh",
            json={"refresh_token": refresh_token},
            headers={"Content-Type": "application/json"}
        )
        
        success = response.status_code == 200
        print_result("Token Refresh", success, f"Status: {response.status_code}")
        
        if success:
            data = response.json()
            print(f"   New Access Token: {data['access_token'][:50]}...")
            return data
        else:
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print_result("Token Refresh", False, str(e))
        return None

def test_invalid_token():
    """Test with invalid token."""
    print_section("Invalid Token Test")
    try:
        headers = {
            "Authorization": "Bearer invalid_token_here",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        
        # Should fail with 401
        success = response.status_code == 401
        print_result("Invalid Token Rejection", success, f"Status: {response.status_code}")
        
        return success
            
    except Exception as e:
        print_result("Invalid Token Rejection", False, str(e))
        return False

def main():
    """Run all authentication tests."""
    print("🔐 JWT Authentication System Test Suite")
    print(f"Testing against: {BASE_URL}")
    print(f"Test time: {datetime.now()}")
    
    # Test results
    results = []
    
    # 1. Health check
    results.append(test_health_check())
    
    # 2. User registration
    registration_data = test_user_registration()
    results.append(registration_data is not None)
    
    if registration_data:
        # 3. Test protected endpoint with registration token
        results.append(test_protected_endpoint(registration_data["access_token"]))
    
    # 4. User login
    login_data = test_user_login()
    results.append(login_data is not None)
    
    if login_data:
        # 5. Test protected endpoint with login token
        results.append(test_protected_endpoint(login_data["access_token"]))
        
        # 6. Test token refresh
        refresh_data = test_token_refresh(login_data["refresh_token"])
        results.append(refresh_data is not None)
    
    # 7. Test invalid token
    results.append(test_invalid_token())
    
    # Summary
    print_section("Test Summary")
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Authentication system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the server logs and configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
