#!/usr/bin/env python3
"""
Check Docker status and provide troubleshooting steps
"""

import subprocess
import sys
import time

def run_command(cmd, timeout=30):
    """Run a command with timeout"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"

def check_docker_status():
    """Check Docker status"""
    print("🔍 Checking Docker status...")
    
    # Check if Docker is running
    code, stdout, stderr = run_command("docker --version")
    if code != 0:
        print("❌ Docker is not installed or not running")
        return False
    
    print(f"✅ Docker version: {stdout.strip()}")
    
    # Check Docker Compose
    code, stdout, stderr = run_command("docker-compose --version")
    if code != 0:
        print("❌ Docker Compose is not available")
        return False
    
    print(f"✅ Docker Compose version: {stdout.strip()}")
    
    return True

def check_containers():
    """Check container status"""
    print("\n🔍 Checking container status...")
    
    code, stdout, stderr = run_command("docker ps -a")
    if code == 0:
        print("📋 Container status:")
        print(stdout)
    else:
        print(f"❌ Failed to check containers: {stderr}")
    
    # Check if our containers are running
    code, stdout, stderr = run_command("docker-compose -f docker-compose.dev.yml ps")
    if code == 0:
        print("\n📋 Project containers:")
        print(stdout)
    else:
        print(f"❌ Failed to check project containers: {stderr}")

def check_logs():
    """Check container logs"""
    print("\n🔍 Checking recent logs...")
    
    code, stdout, stderr = run_command("docker-compose -f docker-compose.dev.yml logs --tail=20 app", timeout=10)
    if code == 0:
        print("📋 Recent app logs:")
        print(stdout)
    else:
        print(f"❌ Failed to get logs: {stderr}")

def cleanup_docker():
    """Clean up Docker resources"""
    print("\n🧹 Cleaning up Docker resources...")
    
    # Stop containers
    print("Stopping containers...")
    run_command("docker-compose -f docker-compose.dev.yml down")
    
    # Remove unused images
    print("Removing unused images...")
    run_command("docker image prune -f")
    
    # Remove unused volumes
    print("Removing unused volumes...")
    run_command("docker volume prune -f")
    
    print("✅ Cleanup complete")

def quick_restart():
    """Quick restart of the backend"""
    print("\n🔄 Quick restart...")
    
    # Stop everything
    print("Stopping services...")
    run_command("docker-compose -f docker-compose.dev.yml down")
    
    # Start only the database first
    print("Starting database...")
    code, stdout, stderr = run_command("docker-compose -f docker-compose.dev.yml up -d mysql_dev")
    
    if code == 0:
        print("✅ Database started")
        
        # Wait a bit for database to be ready
        print("Waiting for database to be ready...")
        time.sleep(10)
        
        # Start the app
        print("Starting app...")
        code, stdout, stderr = run_command("docker-compose -f docker-compose.dev.yml up app")
        
        if code == 0:
            print("✅ App started successfully")
        else:
            print(f"❌ App failed to start: {stderr}")
    else:
        print(f"❌ Database failed to start: {stderr}")

def main():
    """Main function"""
    print("🐳 Docker Status Checker")
    print("=" * 50)
    
    if not check_docker_status():
        return
    
    check_containers()
    check_logs()
    
    print("\n" + "=" * 50)
    print("🛠️  Troubleshooting Options:")
    print("1. Run 'python quick_start.py' for a non-Docker version")
    print("2. Run this script with 'cleanup' argument to clean Docker")
    print("3. Run this script with 'restart' argument to restart services")
    print("4. Check if port 8000 is already in use: 'lsof -i :8000'")
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "cleanup":
            cleanup_docker()
        elif sys.argv[1] == "restart":
            quick_restart()

if __name__ == "__main__":
    main()
