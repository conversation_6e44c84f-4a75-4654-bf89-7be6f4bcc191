services:
  nginx:
    image: nginx:1.25-alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro  # For SSL certificates
    depends_on:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    expose:
      - "8000"
    environment:
      # Production environment
      APP_ENV: production

      # MySQL Database Configuration
      PROD_DATABASE_URL: mysql+pymysql://saas_user:${MYSQL_PASSWORD}@mysql:3306/saas_prod_db

      # Authentication
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}

      # Database Pool Settings
      DB_POOL_SIZE: 15
      DB_MAX_OVERFLOW: 25
      DB_POOL_RECYCLE: 3600

      # Production server settings
      WORKERS: 4
      MAX_REQUESTS: 1000
      MAX_REQUESTS_JITTER: 100
      TIMEOUT: 30
      KEEP_ALIVE: 5

      # Security settings
      TRUSTED_HOSTS: ${TRUSTED_HOSTS:-*}

    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./backups:/app/backups
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: saas_prod_db
      MYSQL_USER: saas_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=200
      --innodb_buffer_pool_size=256M
      --innodb_log_file_size=64M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped

volumes:
  mysql_data:
