-- Create additional databases for development and testing
CREATE DATABASE IF NOT EXISTS saas_dev_db;
CREATE DATABASE IF NOT EXISTS saas_test_db;
CREATE DATABASE IF NOT EXISTS saas_prod_db;

-- Grant permissions to saas_user for all databases
GRANT ALL PRIVILEGES ON saas_dev_db.* TO 'saas_user'@'%';
GRANT ALL PRIVILEGES ON saas_test_db.* TO 'saas_user'@'%';
GRANT ALL PRIVILEGES ON saas_prod_db.* TO 'saas_user'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Show all databases
SHOW DATABASES;
