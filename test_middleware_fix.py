#!/usr/bin/env python3
"""
Quick test to verify the middleware import fix.
"""

def test_middleware_imports():
    """Test that all middleware imports work correctly."""
    print("🔍 Testing middleware imports...")
    
    try:
        # Test the corrected import
        from starlette.middleware.base import BaseHTTPMiddleware
        print("✅ BaseHTTPMiddleware import successful")
        
        # Test our API versioning middleware
        from api_versioning import APIVersionMiddleware
        print("✅ APIVersionMiddleware import successful")
        
        # Test other middleware
        from security_middleware import SecurityHeadersMiddleware
        print("✅ SecurityHeadersMiddleware import successful")
        
        from enhanced_security import EnhancedSecurityMiddleware
        print("✅ EnhancedSecurityMiddleware import successful")
        
        print("\n🎉 All middleware imports are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Middleware import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_middleware_imports()
    exit(0 if success else 1)
